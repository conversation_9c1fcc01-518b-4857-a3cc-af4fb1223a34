<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, viewport-fit=cover">
    <title>FamiFinanzas Pro - Control de Gastos Familiares</title>

    <!-- Meta tags for PWA -->
    <meta name="description" content="Aplicación avanzada para control de gastos familiares optimizada para Chile">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="FamiFinanzas Pro">

    <!-- Additional mobile optimization meta tags -->
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- External CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
            overflow-x: hidden;
            overflow-y: auto;
            -webkit-text-size-adjust: 100%;
            -webkit-tap-highlight-color: transparent;
            -webkit-overflow-scrolling: touch;
            height: 100%;
            position: relative;
        }

        html {
            height: 100%;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Prevent zoom on input focus */
        input, select, textarea {
            font-size: 16px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* Improve touch targets */
        button, .nav-item, .fab-item, [onclick] {
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        /* Ensure proper scrolling on all containers */
        .fade-in, .space-y-6, .space-y-4, .space-y-3 {
            overflow: visible;
            height: auto;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: -280px;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            transition: left 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar.open {
            left: 0;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .main-content {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
            padding-bottom: 100px;
            min-height: calc(100vh - 160px);
        }

        .mobile-header {
            background: white;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            color: #6b7280;
            text-decoration: none;
            font-size: 12px;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item i {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .fab {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 200;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .fab-menu {
            position: fixed;
            bottom: 160px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
            z-index: 199;
        }

        .fab-menu.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .fab-item {
            display: flex;
            align-items: center;
            background: white;
            padding: 12px 16px;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .fab-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .fab-item i {
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--error-color);
        }

        .notification.info {
            background: var(--primary-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .mini-chart {
            position: relative;
            height: 30px;
            width: 60px;
            margin-left: auto;
        }

        .mini-chart-bar {
            position: absolute;
            bottom: 0;
            width: 8px;
            background: currentColor;
            border-radius: 2px 2px 0 0;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .mini-chart-bar:hover {
            opacity: 1;
        }

        .swipe-indicator {
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 12px;
            border-radius: 50%;
            font-size: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 300;
        }

        .swipe-indicator.left {
            left: 20px;
        }

        .swipe-indicator.right {
            right: 20px;
        }

        .swipe-indicator.visible {
            opacity: 0.7;
        }

        /* Mobile optimizations */
        .mobile-card {
            transition: all 0.2s ease;
        }

        .mobile-card:active {
            transform: scale(0.98);
            background-color: #f3f4f6;
        }

        .no-swipe {
            touch-action: pan-x pan-y;
        }

        .overflow-x-auto {
            touch-action: pan-x;
            -webkit-overflow-scrolling: touch;
        }

        /* Enhanced mobile styles */
        @media (max-width: 768px) {
            /* Ensure minimum touch target sizes */
            .mobile-action-btn, button, .nav-item, .fab-item {
                min-height: 44px;
                min-width: 44px;
                font-size: 14px;
                padding: 8px 16px;
                touch-action: manipulation;
            }

            /* Larger text for better readability */
            .text-sm {
                font-size: 16px !important;
            }

            .text-xs {
                font-size: 14px !important;
            }

            /* Better spacing for mobile */
            .mobile-card {
                margin-bottom: 16px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                padding: 16px;
            }

            .mobile-card:hover {
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }

            /* Improved header and navigation */
            .mobile-header {
                padding: 20px;
                min-height: 70px;
            }

            .mobile-header h2 {
                font-size: 20px;
            }

            .refresh-btn {
                min-width: 48px;
                min-height: 48px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 12px;
            }

            .refresh-btn:active {
                transform: scale(0.95);
            }

            /* Better bottom navigation */
            .bottom-nav {
                padding: 12px 0;
                min-height: 80px;
            }

            .bottom-nav .nav-item {
                min-width: 60px;
                padding: 12px 8px;
                font-size: 12px;
            }

            .bottom-nav .nav-item i {
                font-size: 20px;
                margin-bottom: 4px;
            }

            /* Improved FAB */
            .fab {
                width: 64px;
                height: 64px;
                bottom: 100px;
                font-size: 28px;
            }

            .fab-item {
                min-height: 48px;
                padding: 12px 20px;
                font-size: 16px;
            }

            /* Better form elements */
            input, select, textarea {
                min-height: 48px;
                font-size: 16px;
                padding: 12px 16px;
            }

            /* Improved modal */
            .modal {
                margin: 20px;
            }

            /* Better grid spacing */
            .grid {
                gap: 16px;
            }

            /* Improved cards */
            .bg-white {
                border-radius: 16px;
            }

            /* Better balance cards */
            .balance-card {
                padding: 20px;
                min-height: 120px;
            }

            /* Improved insights */
            .insight-card {
                padding: 20px;
                margin-bottom: 16px;
            }

            /* Mobile content optimization */
            .main-content {
                padding-bottom: 120px;
                overflow: visible;
                height: auto;
                min-height: calc(100vh - 140px);
            }

            /* Ensure scrolling works on mobile */
            html, body {
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                height: auto !important;
                position: relative !important;
            }

            /* Remove any height restrictions */
            * {
                max-height: none !important;
            }

            /* Ensure content flows naturally */
            .fade-in, .space-y-6, .space-y-4, .space-y-3 {
                overflow: visible !important;
                height: auto !important;
            }
        }

        /* Theme variations */
        .theme-green {
            --primary-color: #10b981;
            --secondary-color: #047857;
        }

        .theme-purple {
            --primary-color: #8b5cf6;
            --secondary-color: #7c3aed;
        }

        .theme-orange {
            --primary-color: #f59e0b;
            --secondary-color: #d97706;
        }

        /* App mode optimizations */
        .app-mode {
            overflow: hidden;
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
        }

        .app-mode * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        .app-mode input,
        .app-mode textarea,
        .app-mode [contenteditable] {
            -webkit-user-select: text !important;
            user-select: text !important;
        }

        .app-mode .web-only {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Splash Screen -->
    <div id="splashScreen" class="fixed inset-0 bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="mb-6">
                <i class="fas fa-wallet text-6xl mb-4 animate-pulse"></i>
                <h1 class="text-3xl font-bold mb-2">FamiFinanzas Pro</h1>
                <p class="text-blue-200">Control Avanzado de Gastos Familiares</p>
            </div>
            <div class="flex justify-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div id="sidebar" class="sidebar">
        <div class="p-6">
            <div class="flex items-center space-x-3 mb-8">
                <i class="fas fa-wallet text-2xl text-white"></i>
                <div>
                    <h2 class="text-white font-bold text-lg">FamiFinanzas Pro</h2>
                    <p class="text-blue-200 text-sm" id="familyName">Familia Pérez</p>
                </div>
            </div>

            <nav class="space-y-2">
                <a href="#" data-section="resumen" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-home"></i>
                    <span class="sidebar-text">Resumen</span>
                </a>
                <a href="#" data-section="ingresos" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-plus-circle"></i>
                    <span class="sidebar-text">Ingresos</span>
                </a>
                <a href="#" data-section="gastos" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-minus-circle"></i>
                    <span class="sidebar-text">Gastos</span>
                </a>
                <a href="#" data-section="estadisticas" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">Estadísticas</span>
                </a>
                <a href="#" data-section="categorias" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-tags"></i>
                    <span class="sidebar-text">Categorías</span>
                </a>
                <a href="#" data-section="presupuestos" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-calendar"></i>
                    <span class="sidebar-text">Presupuestos</span>
                </a>
                <a href="#" data-section="metas" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-bullseye"></i>
                    <span class="sidebar-text">Metas de Ahorro</span>
                </a>
                <a href="#" data-section="recurrentes" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-redo"></i>
                    <span class="sidebar-text">Gastos Recurrentes</span>
                </a>
                <a href="#" data-section="ajustes" class="nav-item flex items-center space-x-3 p-3 rounded-lg hover:bg-blue-700 text-blue-200 hover:text-white transition">
                    <i class="fas fa-cog"></i>
                    <span class="sidebar-text">Ajustes</span>
                </a>
            </nav>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>

    <!-- Mobile Header -->
    <div class="mobile-header md:hidden">
        <div class="flex items-center justify-between w-full">
            <div class="flex items-center space-x-3">
                <button id="mobileMenuBtn" class="mobile-menu-btn bg-blue-600 text-white hover:bg-blue-700 p-3 rounded-lg shadow-md transition-colors">
                    <i class="fas fa-bars text-lg"></i>
                </button>
                <h2 class="text-xl font-semibold text-gray-800" id="pageTitle">Resumen Financiero</h2>
            </div>
            <button onclick="refreshCurrentSection()" class="refresh-btn bg-gray-100 hover:bg-gray-200 text-gray-600 p-2 rounded-lg transition-colors" title="Actualizar datos">
                <i class="fas fa-sync-alt text-sm"></i>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div id="mainContent" class="main-content">
        <!-- Content will be loaded here -->
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav md:hidden" role="navigation" aria-label="Navegación principal">
        <a href="#" data-section="resumen" class="nav-item active" role="button" aria-label="Ir a Resumen" tabindex="0">
            <i class="fas fa-home" aria-hidden="true"></i>
            <span>Inicio</span>
        </a>
        <a href="#" data-section="gastos" class="nav-item" role="button" aria-label="Ir a Gastos" tabindex="0">
            <i class="fas fa-minus-circle" aria-hidden="true"></i>
            <span>Gastos</span>
        </a>
        <a href="#" data-section="metas" class="nav-item" role="button" aria-label="Ir a Metas" tabindex="0">
            <i class="fas fa-bullseye" aria-hidden="true"></i>
            <span>Metas</span>
        </a>
        <a href="#" data-section="estadisticas" class="nav-item" role="button" aria-label="Ir a Estadísticas" tabindex="0">
            <i class="fas fa-chart-bar" aria-hidden="true"></i>
            <span>Stats</span>
        </a>
        <a href="#" data-section="ajustes" class="nav-item" role="button" aria-label="Ir a Configuración" tabindex="0">
            <i class="fas fa-cog" aria-hidden="true"></i>
            <span>Config</span>
        </a>
    </nav>

    <!-- Floating Action Button -->
    <div id="fab" class="fab">
        <i class="fas fa-plus"></i>
    </div>

    <!-- FAB Menu -->
    <div id="fabMenu" class="fab-menu">
        <div class="fab-item" onclick="showAddTransactionModal('income')">
            <i class="fas fa-plus-circle text-green-600"></i>
            <span>Agregar Ingreso</span>
        </div>
        <div class="fab-item" onclick="showAddTransactionModal('expense')">
            <i class="fas fa-minus-circle text-red-600"></i>
            <span>Agregar Gasto</span>
        </div>
        <div class="fab-item" onclick="loadSection('metas')">
            <i class="fas fa-bullseye text-purple-600"></i>
            <span>Nueva Meta</span>
        </div>
    </div>

    <!-- Swipe Indicators -->
    <div id="swipeLeft" class="swipe-indicator left">
        <i class="fas fa-chevron-left"></i>
    </div>
    <div id="swipeRight" class="swipe-indicator right">
        <i class="fas fa-chevron-right"></i>
    </div>

    <!-- Transaction Modal -->
    <div id="transactionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-xl p-6 w-full max-w-md mx-auto modal">
            <div class="flex justify-between items-center mb-6">
                <h3 id="modalTitle" class="text-xl font-semibold text-gray-800">Agregar Transacción</h3>
                <button onclick="closeTransactionModal()" class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>

            <form id="transactionForm" class="space-y-6">
                <div>
                    <label class="block text-base font-medium text-gray-700 mb-2">Tipo</label>
                    <select id="transactionType" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none">
                        <option value="expense">Gasto</option>
                        <option value="income">Ingreso</option>
                    </select>
                </div>

                <div>
                    <label class="block text-base font-medium text-gray-700 mb-2">Descripción</label>
                    <input type="text" id="transactionDescription" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none" placeholder="Ej: Supermercado, Sueldo, etc." required>
                </div>

                <div>
                    <label class="block text-base font-medium text-gray-700 mb-2">Categoría</label>
                    <select id="transactionCategory" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>

                <div>
                    <label class="block text-base font-medium text-gray-700 mb-2">Monto ($)</label>
                    <input type="number" id="transactionAmount" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none" placeholder="0" required>
                </div>

                <div>
                    <label class="block text-base font-medium text-gray-700 mb-2">Fecha</label>
                    <input type="date" id="transactionDate" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none" required>
                </div>

                <div class="flex space-x-4 pt-6">
                    <button type="button" onclick="closeTransactionModal()" class="flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-lg hover:bg-gray-300 transition font-medium text-base">
                        Cancelar
                    </button>
                    <button type="submit" class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition font-medium text-base">
                        Guardar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Enhanced global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);

            // Log error details for debugging
            const errorInfo = {
                message: e.error?.message || e.message || 'Unknown error',
                filename: e.filename || 'Unknown file',
                lineno: e.lineno || 'Unknown line',
                colno: e.colno || 'Unknown column',
                stack: e.error?.stack || 'No stack trace',
                timestamp: new Date().toISOString()
            };

            console.error('Error details:', errorInfo);

            // Show user-friendly error message
            if (typeof showNotification === 'function') {
                // Don't show errors for known issues that are handled elsewhere
                const message = e.error?.message || e.message || '';
                if (!message.includes('saveAppData') &&
                    !message.includes('Script error') &&
                    !message.includes('Non-Error promise rejection')) {
                    showNotification('Se produjo un error inesperado', 'error');
                }
            }
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);

            // Prevent the default browser behavior
            event.preventDefault();

            // Show user-friendly message
            if (typeof showNotification === 'function') {
                showNotification('Error de conexión o procesamiento', 'error');
            }
        });

        // Global app state with all advanced features
        const app = {
            state: {
                currentSection: 'resumen',
                currentTheme: 'default',
                fabMenuOpen: false,
                sidebarOpen: false,
                isInitialized: false
            },
            data: {
                transactions: [
                    // Datos de ejemplo para testing
                    {
                        id: 1,
                        type: 'income',
                        description: 'Sueldo Enero',
                        category: 'salary',
                        amount: 850000,
                        date: new Date().toISOString().slice(0, 10)
                    },
                    {
                        id: 2,
                        type: 'expense',
                        description: 'Supermercado',
                        category: 'food',
                        amount: 45000,
                        date: new Date().toISOString().slice(0, 10)
                    },
                    {
                        id: 3,
                        type: 'expense',
                        description: 'Gasolina',
                        category: 'transport',
                        amount: 35000,
                        date: new Date(Date.now() - 86400000).toISOString().slice(0, 10)
                    }
                ],
                customCategories: { income: [], expense: [] },
                budgets: {},
                familyName: 'Familia Pérez',
                savingsGoals: [],
                recurringExpenses: [],
                userLocation: null,
                insights: {
                    lastAnalysis: null,
                    suggestions: []
                }
            },
            cache: {},
            currencyRates: null,
            charts: {
                category: null,
                trend: null
            },
            timers: {
                notifications: [],
                intervals: [],
                timeouts: []
            },
            eventListeners: []
        };

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check if external dependencies are loaded
            checkDependencies();

            setTimeout(() => {
                const splashScreen = document.getElementById('splashScreen');
                if (splashScreen) {
                    splashScreen.style.display = 'none';
                }
                initializeApp();
            }, 2500);
        });

        function checkDependencies() {
            const dependencies = {
                'Tailwind CSS': () => !!document.querySelector('script[src*="tailwindcss"]'),
                'Font Awesome': () => !!document.querySelector('link[href*="font-awesome"]'),
                'Chart.js': () => typeof Chart !== 'undefined'
            };

            Object.entries(dependencies).forEach(([name, check]) => {
                if (!check()) {
                    console.warn(`${name} not loaded properly`);
                }
            });

            // Wait for Chart.js to load
            if (typeof Chart === 'undefined') {
                console.log('Waiting for Chart.js to load...');
                const checkChart = setInterval(() => {
                    if (typeof Chart !== 'undefined') {
                        console.log('Chart.js loaded successfully');
                        clearInterval(checkChart);
                    }
                }, 500);
            }
        }

        function initializeApp() {
            try {
                console.log('Initializing FamiFinanzas Pro...');

                // Load data first
                loadData();

                // Load saved theme
                loadSavedTheme();

                // Set up event listeners
                setupEventListeners();

                // Load initial section
                loadSection('resumen');

                // Detect if running in app wrapper
                detectAppEnvironment();

                // Initialize notifications
                initializeNotifications();

                // Show mobile welcome message if first time
                if (window.innerWidth <= 768 && !localStorage.getItem('mobile_welcome_shown')) {
                    setTimeout(() => {
                        const isApp = isRunningInApp();
                        const message = isApp ?
                            '¡Bienvenido a FamiFinanzas Pro! Desliza entre secciones para navegar' :
                            '¡Bienvenido! Desliza entre secciones o usa el menú ☰';
                        showNotification(message, 'info');
                        localStorage.setItem('mobile_welcome_shown', 'true');
                    }, 3000);
                }

                // Performance optimization
                optimizePerformance();

                // Initialize internet-connected features with delay
                setTimeout(() => {
                    initializeInternetFeatures();
                }, 2000);

                // Mark app as initialized
                app.state.isInitialized = true;

                console.log('FamiFinanzas Pro initialized successfully');

                // Ensure mobile scrolling works
                ensureMobileScrolling();

                // Final validation
                validateAppState();

            } catch (error) {
                console.error('Error initializing app:', error);
                showNotification('Error al inicializar la aplicación', 'error');
            }
        }

        function validateAppState() {
            const requiredElements = [
                'splashScreen', 'sidebar', 'mainContent', 'fab', 'fabMenu',
                'mobileMenuBtn', 'pageTitle', 'sidebarOverlay'
            ];

            const missingElements = requiredElements.filter(id => !document.getElementById(id));

            if (missingElements.length > 0) {
                console.warn('Missing elements:', missingElements);
            }

            // Validate data structure
            if (!app.data.transactions || !Array.isArray(app.data.transactions)) {
                console.warn('Invalid transactions data structure');
                app.data.transactions = [];
            }

            if (!app.data.savingsGoals || !Array.isArray(app.data.savingsGoals)) {
                console.warn('Invalid savings goals data structure');
                app.data.savingsGoals = [];
            }

            console.log('App state validation completed');
        }

        function getRecurringExpensesSection() {
            const recurringExpenses = app.data.recurringExpenses || [];

            if (recurringExpenses.length === 0) {
                return `
                    <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800">💳 Gastos Recurrentes</h3>
                            <button onclick="showCreateRecurringModal()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition">
                                <i class="fas fa-plus mr-2"></i>Agregar
                            </button>
                        </div>
                        <div class="text-center py-8">
                            <i class="fas fa-repeat text-gray-300 text-4xl mb-4"></i>
                            <p class="text-gray-500 mb-4">No tienes gastos recurrentes configurados</p>
                            <button onclick="showCreateRecurringModal()" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition">
                                Crear Primer Gasto Recurrente
                            </button>
                        </div>
                    </div>
                `;
            }

            const totalMonthly = recurringExpenses
                .filter(expense => expense.isActive)
                .reduce((sum, expense) => {
                    switch(expense.frequency) {
                        case 'monthly': return sum + expense.amount;
                        case 'weekly': return sum + (expense.amount * 4.33);
                        case 'biweekly': return sum + (expense.amount * 2);
                        case 'quarterly': return sum + (expense.amount / 3);
                        case 'yearly': return sum + (expense.amount / 12);
                        default: return sum;
                    }
                }, 0);

            return `
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-800">💳 Gastos Recurrentes</h3>
                        <button onclick="showCreateRecurringModal()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm transition">
                            <i class="fas fa-plus mr-2"></i>Agregar
                        </button>
                    </div>

                    <div class="bg-purple-50 p-4 rounded-lg mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-purple-700 font-medium">Total Mensual Estimado:</span>
                            <span class="text-purple-800 font-bold text-lg">${formatCLP(totalMonthly)}</span>
                        </div>
                    </div>

                    <div class="space-y-3">
                        ${recurringExpenses.map(expense => `
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg ${!expense.isActive ? 'opacity-50' : ''}">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <p class="font-medium text-gray-800">${expense.description}</p>
                                        <span class="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full">${getFrequencyLabel(expense.frequency)}</span>
                                        ${!expense.isActive ? '<span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Inactivo</span>' : ''}
                                    </div>
                                    <p class="text-sm text-gray-600">${getCategoryLabel(expense.category, 'expense')}</p>
                                    <p class="text-xs text-gray-500">Próximo: ${formatDate(expense.nextDate)}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold text-gray-800">${formatCLP(expense.amount)}</p>
                                    <div class="flex space-x-1 mt-1">
                                        <button onclick="toggleRecurringExpense('${expense.id}')" class="text-xs text-blue-600 hover:text-blue-800">
                                            ${expense.isActive ? 'Pausar' : 'Activar'}
                                        </button>
                                        <button onclick="deleteRecurringExpense('${expense.id}')" class="text-xs text-red-600 hover:text-red-800">
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function getFrequencyLabel(frequency) {
            const labels = {
                'monthly': 'Mensual',
                'weekly': 'Semanal',
                'biweekly': 'Quincenal',
                'quarterly': 'Trimestral',
                'yearly': 'Anual'
            };
            return labels[frequency] || frequency;
        }

        function toggleRecurringExpense(id) {
            try {
                const expense = app.data.recurringExpenses.find(e => e.id === id);
                if (expense) {
                    expense.isActive = !expense.isActive;
                    saveData();
                    loadSection('presupuestos');
                    showNotification(`Gasto recurrente ${expense.isActive ? 'activado' : 'pausado'}`, 'success');
                    vibrate(50);
                } else {
                    showNotification('Gasto recurrente no encontrado', 'error');
                }
            } catch (error) {
                console.error('Error toggling recurring expense:', error);
                showNotification('Error al cambiar estado del gasto recurrente', 'error');
            }
        }

        function deleteRecurringExpense(id) {
            try {
                if (confirm('¿Estás seguro de que deseas eliminar este gasto recurrente?')) {
                    app.data.recurringExpenses = app.data.recurringExpenses.filter(e => e.id !== id);
                    saveData();
                    loadSection('presupuestos');
                    showNotification('Gasto recurrente eliminado', 'success');
                    vibrate(100);
                }
            } catch (error) {
                console.error('Error deleting recurring expense:', error);
                showNotification('Error al eliminar gasto recurrente', 'error');
            }
        }

        function ensureMobileScrolling() {
            // Force enable scrolling on mobile devices
            if (window.innerWidth <= 768) {
                console.log('Ensuring mobile scrolling is enabled');

                // Remove any overflow restrictions
                document.body.style.overflow = 'auto';
                document.body.style.overflowY = 'auto';
                document.body.style.webkitOverflowScrolling = 'touch';
                document.body.style.height = 'auto';
                document.body.style.maxHeight = 'none';

                // Ensure main content is scrollable
                const mainContent = document.getElementById('mainContent');
                if (mainContent) {
                    mainContent.style.overflow = 'visible';
                    mainContent.style.height = 'auto';
                    mainContent.style.maxHeight = 'none';
                }

                // Remove any touch-action restrictions that might interfere
                document.body.style.touchAction = 'auto';

                console.log('Mobile scrolling optimizations applied');
            }
        }

        function loadData() {
            try {
                const savedData = localStorage.getItem('famifinanzas_data');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    app.data = { ...app.data, ...data };
                } else {
                    // Default data with examples
                    app.data.transactions = [
                        { id: 1, type: 'expense', category: 'food', amount: 85990, description: 'Supermercado Jumbo', date: '2024-01-15' },
                        { id: 2, type: 'income', category: 'salary', amount: 1200000, description: 'Sueldo Enero', date: '2024-01-10' },
                        { id: 3, type: 'expense', category: 'utilities', amount: 45320, description: 'Cuenta de luz CGE', date: '2024-01-05' },
                        { id: 4, type: 'expense', category: 'transport', amount: 25000, description: 'Bencina Copec', date: '2024-01-12' }
                    ];
                }

                // Ensure all new properties exist
                if (!app.data.savingsGoals) app.data.savingsGoals = [];
                if (!app.data.recurringExpenses) app.data.recurringExpenses = [];
                if (!app.data.insights) app.data.insights = { lastAnalysis: null, suggestions: [] };
                if (!app.data.customCategories) app.data.customCategories = { income: [], expense: [] };
                if (!app.data.budgets) app.data.budgets = {};
                if (!app.data.userLocation) app.data.userLocation = null;

                // Load family name
                const familyName = localStorage.getItem('familyName');
                if (familyName) {
                    app.data.familyName = familyName;
                    const familyNameEl = document.getElementById('familyName');
                    if (familyNameEl) familyNameEl.textContent = familyName;
                }
            } catch (error) {
                console.error('Error loading data:', error);
                showNotification('Error al cargar datos', 'error');
            }
        }

        function saveData() {
            try {
                localStorage.setItem('famifinanzas_data', JSON.stringify(app.data));
            } catch (error) {
                console.error('Error saving data:', error);
                showNotification('Error al guardar datos', 'error');
            }
        }

        function loadSavedTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                app.state.currentTheme = savedTheme;
                document.body.className = savedTheme !== 'default' ? 'theme-' + savedTheme : '';
            }
        }

        function setupEventListeners() {
            // Mobile menu button
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', toggleSidebar);
            }

            // Sidebar overlay
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            // Navigation items - Use event delegation for better performance
            document.addEventListener('click', function(e) {
                const navItem = e.target.closest('[data-section]');
                if (navItem) {
                    e.preventDefault();
                    const section = navItem.getAttribute('data-section');
                    loadSection(section);
                    closeSidebar();
                    return;
                }

                // Handle modal close clicks
                if (e.target.closest('[onclick*="closeTransactionModal"]')) {
                    closeTransactionModal();
                    return;
                }

                // Handle FAB menu items
                if (e.target.closest('[onclick*="showAddTransactionModal"]')) {
                    const type = e.target.closest('[onclick*="income"]') ? 'income' : 'expense';
                    showAddTransactionModal(type);
                    return;
                }
            });

            // FAB menu
            const fab = document.getElementById('fab');
            if (fab) {
                fab.addEventListener('click', toggleFabMenu);
            }

            // Transaction form - Will be set up when modal is created
            setupTransactionFormListener();

            // Touch gestures
            setupTouchGestures();

            // Service worker registration
            registerServiceWorker();

            // Add resize listener to maintain mobile scrolling
            window.addEventListener('resize', () => {
                setTimeout(ensureMobileScrolling, 100);
            });
        }

        function setupTransactionFormListener() {
            // This will be called after the modal is in the DOM
            setTimeout(() => {
                const transactionForm = document.getElementById('transactionForm');
                if (transactionForm && !transactionForm.hasAttribute('data-listener-added')) {
                    transactionForm.addEventListener('submit', handleTransactionSubmit);
                    transactionForm.setAttribute('data-listener-added', 'true');
                }
            }, 100);
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            app.state.sidebarOpen = !app.state.sidebarOpen;

            if (app.state.sidebarOpen) {
                sidebar.classList.add('open');
                overlay.classList.add('active');
            } else {
                sidebar.classList.remove('open');
                overlay.classList.remove('active');
            }
        }

        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            app.state.sidebarOpen = false;
            sidebar.classList.remove('open');
            overlay.classList.remove('active');
        }

        function toggleFabMenu() {
            const fabMenu = document.getElementById('fabMenu');
            const fab = document.getElementById('fab');

            app.state.fabMenuOpen = !app.state.fabMenuOpen;

            if (app.state.fabMenuOpen) {
                fabMenu.classList.add('active');
                fab.innerHTML = '<i class="fas fa-times"></i>';
            } else {
                fabMenu.classList.remove('active');
                fab.innerHTML = '<i class="fas fa-plus"></i>';
            }
        }

        function loadSection(sectionName) {
            app.state.currentSection = sectionName;

            // Update page title
            const titles = {
                'resumen': 'Resumen Financiero',
                'ingresos': 'Gestión de Ingresos',
                'gastos': 'Gestión de Gastos',
                'estadisticas': 'Estadísticas Financieras',
                'categorias': 'Gestión de Categorías',
                'presupuestos': 'Gestión de Presupuestos',
                'metas': 'Metas de Ahorro',
                'recurrentes': 'Gastos Recurrentes',
                'ajustes': 'Configuración y Ajustes'
            };

            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle) {
                pageTitle.textContent = titles[sectionName] || 'FamiFinanzas Pro';
            }

            // Update navigation active states
            updateNavigationStates(sectionName);

            // Load section content
            const content = getSectionContent(sectionName);
            const mainContent = document.getElementById('mainContent');
            if (mainContent) {
                mainContent.innerHTML = content;

                // Add fade-in animation
                mainContent.style.opacity = '0';
                setTimeout(() => {
                    mainContent.style.opacity = '1';
                    mainContent.style.transition = 'opacity 0.3s ease';
                }, 50);
            }

            // Initialize section-specific functionality with delay
            setTimeout(() => {
                initializeSectionFeatures(sectionName);
            }, 100);
        }

        function updateNavigationStates(activeSection) {
            // Update sidebar navigation
            document.querySelectorAll('.sidebar .nav-item').forEach(item => {
                const section = item.getAttribute('data-section');
                if (section === activeSection) {
                    item.classList.add('bg-blue-700', 'text-white');
                    item.classList.remove('text-blue-200');
                } else {
                    item.classList.remove('bg-blue-700', 'text-white');
                    item.classList.add('text-blue-200');
                }
            });

            // Update bottom navigation
            document.querySelectorAll('.bottom-nav .nav-item').forEach(item => {
                const section = item.getAttribute('data-section');
                if (section === activeSection) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        function getSectionContent(sectionName) {
            switch(sectionName) {
                case 'resumen':
                    return getResumenContent();
                case 'ingresos':
                    return getIngresosContent();
                case 'gastos':
                    return getGastosContent();
                case 'estadisticas':
                    return getEstadisticasContent();
                case 'categorias':
                    return getCategoriasContent();
                case 'presupuestos':
                    return getPresupuestosContent();
                case 'metas':
                    return getMetasContent();
                case 'recurrentes':
                    return getRecurrentesContent();
                case 'ajustes':
                    return getAjustesContent();
                default:
                    return getResumenContent();
            }
        }

        function getResumenContent() {
            const currentMonth = new Date().toISOString().slice(0, 7);
            const totalIncome = app.data.transactions
                .filter(t => t.type === 'income' && t.date.startsWith(currentMonth))
                .reduce((sum, t) => sum + t.amount, 0);

            const totalExpenses = app.data.transactions
                .filter(t => t.type === 'expense' && t.date.startsWith(currentMonth))
                .reduce((sum, t) => sum + t.amount, 0);

            const balance = totalIncome - totalExpenses;

            return `
                <div class="fade-in">
                    <!-- Smart Insights -->
                    ${getSmartInsightsHTML()}

                    <!-- Balance Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-white rounded-xl shadow-sm p-6 balance-card mobile-card">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-600 mb-2">Ingresos del Mes</p>
                                    <p class="text-xl md:text-2xl font-bold text-green-600 break-words">${formatCLP(totalIncome)}</p>
                                </div>
                                <div class="bg-green-100 p-3 rounded-full ml-4 flex-shrink-0">
                                    <i class="fas fa-arrow-up text-green-600 text-lg"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 balance-card mobile-card">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-600 mb-2">Gastos del Mes</p>
                                    <p class="text-xl md:text-2xl font-bold text-red-600 break-words">${formatCLP(totalExpenses)}</p>
                                </div>
                                <div class="bg-red-100 p-3 rounded-full ml-4 flex-shrink-0">
                                    <i class="fas fa-arrow-down text-red-600 text-lg"></i>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm p-6 balance-card mobile-card">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-600 mb-2">Balance</p>
                                    <p class="text-xl md:text-2xl font-bold break-words ${balance >= 0 ? 'text-green-600' : 'text-red-600'}">${formatCLP(balance)}</p>
                                </div>
                                <div class="p-3 rounded-full ml-4 flex-shrink-0 ${balance >= 0 ? 'bg-green-100' : 'bg-red-100'}">
                                    <i class="fas text-lg ${balance >= 0 ? 'fa-check text-green-600' : 'fa-exclamation text-red-600'}"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Internet-Connected Features -->
                    <div class="space-y-6">
                        ${addCurrencyConverter()}
                        ${addPriceComparator()}
                        ${addNationalComparison()}
                        ${addLocationFeatures()}
                    </div>

                    <!-- Recent Transactions -->
                    <div class="bg-white rounded-xl shadow-sm p-6 mt-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Transacciones Recientes</h3>
                        <div id="recentTransactions">
                            ${getRecentTransactionsHTML()}
                        </div>
                    </div>
                </div>
            `;
        }

        function getSmartInsightsHTML() {
            try {
                const insights = generateSmartInsights();

                if (insights.length === 0) {
                    return '';
                }

                return `
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-sm p-6 mb-6 text-white">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-brain text-2xl"></i>
                                <div>
                                    <h3 class="font-semibold text-lg">Análisis Inteligente</h3>
                                    <p class="text-sm opacity-80">Insights automáticos sobre tus finanzas</p>
                                </div>
                            </div>
                            <button onclick="refreshInsights()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg text-sm transition flex items-center space-x-2">
                                <i class="fas fa-sync-alt"></i>
                                <span class="hidden sm:inline">Actualizar</span>
                            </button>
                        </div>

                        <div class="space-y-4">
                            ${insights.slice(0, 3).map(insight => `
                                <div class="bg-white bg-opacity-10 rounded-lg p-4">
                                    <div class="flex items-start space-x-3">
                                        <i class="${insight.icon} text-xl mt-1 flex-shrink-0"></i>
                                        <div class="flex-1">
                                            <p class="font-medium text-base mb-2">${insight.title}</p>
                                            <p class="text-sm opacity-90 leading-relaxed">${insight.description}</p>
                                            ${insight.tip ? `<p class="text-xs opacity-75 mt-2 italic">💡 ${insight.tip}</p>` : ''}
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('Error generating smart insights:', error);
                return '';
            }
        }

        function generateSmartInsights() {
            try {
                const insights = [];
                const currentMonth = new Date().toISOString().slice(0, 7);
                const lastMonthDate = new Date();
                lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
                const lastMonth = lastMonthDate.toISOString().slice(0, 7);

            // Current month data
            const currentExpenses = app.data.transactions
                .filter(t => t.type === 'expense' && t.date.startsWith(currentMonth))
                .reduce((sum, t) => sum + t.amount, 0);

            const lastMonthExpenses = app.data.transactions
                .filter(t => t.type === 'expense' && t.date.startsWith(lastMonth))
                .reduce((sum, t) => sum + t.amount, 0);

            // Insight 1: Spending comparison
            if (lastMonthExpenses > 0) {
                const change = ((currentExpenses - lastMonthExpenses) / lastMonthExpenses) * 100;
                if (Math.abs(change) > 10) {
                    insights.push({
                        icon: change > 0 ? 'fas fa-arrow-up text-red-300' : 'fas fa-arrow-down text-green-300',
                        title: change > 0 ? 'Gastos Aumentaron' : 'Gastos Disminuyeron',
                        description: `Tus gastos ${change > 0 ? 'aumentaron' : 'disminuyeron'} ${Math.abs(change).toFixed(1)}% comparado con el mes pasado`,
                        tip: change > 0 ? 'Revisa tus gastos en la sección de Estadísticas para identificar dónde puedes reducir' : 'Excelente control de gastos. Mantén este ritmo para mejorar tu situación financiera'
                    });
                }
            }

            // Insight 2: Top spending category
            const categoryExpenses = {};
            app.data.transactions
                .filter(t => t.type === 'expense' && t.date.startsWith(currentMonth))
                .forEach(t => {
                    const category = getCategoryLabel(t.category, 'expense');
                    categoryExpenses[category] = (categoryExpenses[category] || 0) + t.amount;
                });

            const topCategory = Object.entries(categoryExpenses)
                .sort(([,a], [,b]) => b - a)[0];

            if (topCategory && currentExpenses > 0) {
                const percentage = (topCategory[1] / currentExpenses) * 100;
                if (percentage > 30) {
                    insights.push({
                        icon: 'fas fa-chart-pie text-blue-300',
                        title: 'Categoría Dominante',
                        description: `${topCategory[0]} representa ${percentage.toFixed(1)}% de tus gastos (${formatCLP(topCategory[1])})`,
                        tip: 'Considera establecer un presupuesto específico para esta categoría en la sección de Presupuestos'
                    });
                }
            }

            // Insight 3: Savings opportunity
            const avgDailyExpense = currentExpenses / new Date().getDate();
            const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
            const projectedMonthlyExpense = avgDailyExpense * daysInMonth;

            if (projectedMonthlyExpense > currentExpenses * 1.1) {
                insights.push({
                    icon: 'fas fa-piggy-bank text-green-300',
                    title: 'Oportunidad de Ahorro',
                    description: `Si mantienes el ritmo actual, podrías ahorrar ${formatCLP(projectedMonthlyExpense - currentExpenses)} este mes`,
                    tip: 'Aprovecha esta oportunidad creando una meta de ahorro específica en la sección de Metas'
                });
            }

            // Insight 4: Transaction frequency
            const transactionCount = app.data.transactions.filter(t => t.date.startsWith(currentMonth)).length;
            if (transactionCount > 0) {
                const avgPerDay = transactionCount / new Date().getDate();
                if (avgPerDay > 3) {
                    insights.push({
                        icon: 'fas fa-clock text-yellow-300',
                        title: 'Alta Actividad Financiera',
                        description: `Registras un promedio de ${avgPerDay.toFixed(1)} transacciones por día`,
                        tip: 'Considera agrupar compras pequeñas para reducir la frecuencia de gastos'
                    });
                }
            }

            // Insight 5: Weekend vs weekday spending
            const weekendExpenses = app.data.transactions.filter(t => {
                const date = new Date(t.date);
                const day = date.getDay();
                return t.type === 'expense' && t.date.startsWith(currentMonth) && (day === 0 || day === 6);
            }).reduce((sum, t) => sum + t.amount, 0);

            const weekdayExpenses = currentExpenses - weekendExpenses;
            if (weekendExpenses > 0 && weekdayExpenses > 0) {
                const weekendPercentage = (weekendExpenses / currentExpenses) * 100;
                if (weekendPercentage > 40) {
                    insights.push({
                        icon: 'fas fa-calendar-weekend text-purple-300',
                        title: 'Gastos de Fin de Semana',
                        description: `${weekendPercentage.toFixed(1)}% de tus gastos ocurren los fines de semana`,
                        tip: 'Planifica actividades de fin de semana con presupuesto fijo para controlar mejor estos gastos'
                    });
                }
            }

                return insights;
            } catch (error) {
                console.error('Error generating insights:', error);
                return [];
            }
        }

        // Utility functions
        function formatCLP(amount) {
            return new Intl.NumberFormat('es-CL', {
                style: 'currency',
                currency: 'CLP',
                minimumFractionDigits: 0
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('es-CL', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        }

        function getCategoryLabel(category, type) {
            const categories = {
                expense: {
                    food: 'Alimentación',
                    transport: 'Transporte',
                    utilities: 'Servicios Básicos',
                    entertainment: 'Entretenimiento',
                    health: 'Salud',
                    education: 'Educación',
                    clothing: 'Vestuario',
                    home: 'Hogar',
                    other: 'Otros'
                },
                income: {
                    salary: 'Sueldo',
                    freelance: 'Trabajos Independientes',
                    investment: 'Inversiones',
                    bonus: 'Bonos',
                    other: 'Otros'
                }
            };

            return categories[type][category] || category;
        }

        function getRecentTransactionsHTML() {
            try {
                if (!app.data.transactions || !Array.isArray(app.data.transactions)) {
                    return '<p class="text-gray-500 text-center py-4">No hay transacciones registradas</p>';
                }

                const recentTransactions = app.data.transactions
                    .sort((a, b) => new Date(b.date) - new Date(a.date))
                    .slice(0, 5);

                if (recentTransactions.length === 0) {
                    return '<p class="text-gray-500 text-center py-4">No hay transacciones registradas</p>';
                }

                return recentTransactions.map(transaction => {
                const isIncome = transaction.type === 'income';
                const bgClass = isIncome ? 'bg-green-100' : 'bg-red-100';
                const iconClass = isIncome ? 'fa-plus' : 'fa-minus';
                const textColorClass = isIncome ? 'text-green-600' : 'text-red-600';

                return `
                    <div class="flex items-center justify-between p-4 border-b border-gray-100 last:border-b-0 mobile-card">
                        <div class="flex items-center space-x-3 flex-1">
                            <div class="w-12 h-12 rounded-full flex items-center justify-center ${bgClass} flex-shrink-0">
                                <i class="fas ${iconClass} ${textColorClass} text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="font-medium text-gray-900 text-base truncate">${transaction.description}</p>
                                <p class="text-sm text-gray-500 truncate">${getCategoryLabel(transaction.category, transaction.type)} • ${formatDate(transaction.date)}</p>
                            </div>
                        </div>
                        <div class="text-right ml-4 flex-shrink-0">
                            <p class="font-bold ${textColorClass} text-base break-words">${formatCLP(transaction.amount)}</p>
                        </div>
                    </div>
                `;
                }).join('');
            } catch (error) {
                console.error('Error processing transactions:', error);
                return '<p class="text-red-500 text-center py-4">Error al cargar transacciones</p>';
            }
        }

        // Internet-connected features
        function addCurrencyConverter() {
            return `
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <h3 class="font-semibold text-gray-800 mb-4">💱 Conversor de Monedas</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4" id="currencyRates">
                        <div class="text-center p-3 bg-blue-50 rounded-lg">
                            <p class="text-sm text-blue-600">UF</p>
                            <p class="font-bold text-blue-800" id="ufRate">Cargando...</p>
                        </div>
                        <div class="text-center p-3 bg-green-50 rounded-lg">
                            <p class="text-sm text-green-600">UTM</p>
                            <p class="font-bold text-green-800" id="utmRate">Cargando...</p>
                        </div>
                        <div class="text-center p-3 bg-purple-50 rounded-lg">
                            <p class="text-sm text-purple-600">USD</p>
                            <p class="font-bold text-purple-800" id="usdRate">Cargando...</p>
                        </div>
                        <div class="text-center p-3 bg-orange-50 rounded-lg">
                            <p class="text-sm text-orange-600">EUR</p>
                            <p class="font-bold text-orange-800" id="eurRate">Cargando...</p>
                        </div>
                    </div>
                    <div class="mt-4 flex gap-2">
                        <input type="number" id="convertAmount" placeholder="Monto en CLP" class="flex-1 border border-gray-200 rounded-lg px-3 py-2">
                        <select id="convertTo" class="border border-gray-200 rounded-lg px-3 py-2">
                            <option value="uf">A UF</option>
                            <option value="usd">A USD</option>
                            <option value="eur">A EUR</option>
                        </select>
                        <button onclick="convertCurrency()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Convertir</button>
                    </div>
                    <div id="conversionResult" class="mt-2 text-center font-medium"></div>
                </div>
            `;
        }

        function addPriceComparator() {
            return `
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-800">🛒 Comparador de Precios</h3>
                        <button onclick="showPriceComparatorInfo()" class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-info-circle mr-1"></i>¿Cómo funciona?
                        </button>
                    </div>

                    <div class="bg-blue-50 p-3 rounded-lg mb-4">
                        <p class="text-blue-800 text-sm">
                            <i class="fas fa-search mr-2"></i>
                            Compara precios en tiempo real de los principales supermercados de Chile
                        </p>
                    </div>

                    <div class="flex gap-2 mb-4">
                        <input type="text" id="searchProduct" placeholder="Ej: leche, pan, arroz, aceite..." class="flex-1 border-2 border-gray-200 rounded-lg px-4 py-3 focus:border-green-500 focus:outline-none">
                        <button onclick="searchPrices()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition font-medium">
                            <i class="fas fa-search mr-2"></i>Buscar
                        </button>
                    </div>

                    <div id="priceResults" class="space-y-2">
                        <div class="text-center py-8">
                            <i class="fas fa-shopping-cart text-gray-300 text-4xl mb-3"></i>
                            <p class="text-gray-500 mb-2">Busca un producto para comparar precios</p>
                            <p class="text-sm text-gray-400">Productos populares: leche, pan, arroz, aceite, pollo</p>
                        </div>
                    </div>
                </div>

                <!-- National Data Container -->
                <div id="nationalDataContainer">
                    <!-- National data will be loaded here -->
                </div>

                <!-- Additional Features -->
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <h3 class="font-semibold text-gray-800 mb-4">🚀 Funciones Avanzadas</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="loadNationalData()" class="bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition flex items-center justify-center space-x-2 mobile-action-btn">
                            <i class="fas fa-flag"></i>
                            <span>Datos Económicos Chile</span>
                        </button>
                        <button onclick="enableLocationTracking()" class="bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition flex items-center justify-center space-x-2 mobile-action-btn">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Tiendas Cercanas</span>
                        </button>
                        <button onclick="showCreateRecurringModal()" class="bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-lg transition flex items-center justify-center space-x-2 mobile-action-btn">
                            <i class="fas fa-repeat"></i>
                            <span>Gastos Recurrentes</span>
                        </button>
                        <button onclick="showCreateGoalModal()" class="bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition flex items-center justify-center space-x-2 mobile-action-btn">
                            <i class="fas fa-bullseye"></i>
                            <span>Crear Meta Rápida</span>
                        </button>
                    </div>
                </div>

                <!-- National Data Container -->
                <div id="nationalDataContainer">
                    <!-- National data will be loaded here -->
                </div>

                <!-- Nearby Stores Container -->
                <div id="nearbyStores">
                    <!-- Nearby stores will be loaded here -->
                </div>
            `;
        }

        function addNationalComparison() {
            return `
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <h3 class="font-semibold text-gray-800 mb-4">📊 Comparación Nacional</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="p-4 bg-blue-50 rounded-lg">
                            <h4 class="font-medium text-blue-800 mb-2">Tus Gastos vs Promedio Nacional</h4>
                            <div id="nationalComparison">Cargando datos...</div>
                        </div>
                        <div class="p-4 bg-green-50 rounded-lg">
                            <h4 class="font-medium text-green-800 mb-2">Ranking Regional</h4>
                            <div id="regionalRanking">Cargando datos...</div>
                        </div>
                    </div>
                    <button onclick="loadNationalData()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">Actualizar Datos</button>
                </div>
            `;
        }

        function addLocationFeatures() {
            return `
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <h3 class="font-semibold text-gray-800 mb-4">📍 Gastos por Ubicación</h3>
                    <div class="space-y-3">
                        <button onclick="enableLocationTracking()" class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700">
                            Activar Seguimiento de Ubicación
                        </button>
                        <div id="locationStatus" class="text-center text-gray-500">
                            Activa la ubicación para ver tus gastos en el mapa
                        </div>
                        <div id="nearbyStores" class="hidden">
                            <h4 class="font-medium mb-2">Tiendas Cercanas:</h4>
                            <div id="storesList"></div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function refreshCurrentSection() {
            const refreshBtn = document.querySelector('[onclick="refreshCurrentSection()"] i');

            if (refreshBtn) {
                refreshBtn.classList.add('fa-spin');
            }

            setTimeout(() => {
                loadSection(app.state.currentSection);
                if (refreshBtn) {
                    refreshBtn.classList.remove('fa-spin');
                }
                vibrate(50);
                showNotification('Datos actualizados', 'success');
            }, 500);
        }

        // Placeholder functions for other sections
        function getIngresosContent() {
            return `
                <div class="fade-in">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Gestión de Ingresos</h3>
                        <p class="text-gray-600 mb-4">Administra tus fuentes de ingresos de manera eficiente.</p>
                        <button onclick="showAddTransactionModal('income')" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition">
                            <i class="fas fa-plus mr-2"></i>Agregar Ingreso
                        </button>
                    </div>
                </div>
            `;
        }

        function getGastosContent() {
            return `
                <div class="fade-in">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Gestión de Gastos</h3>
                        <p class="text-gray-600 mb-4">Controla y categoriza todos tus gastos familiares.</p>
                        <button onclick="showAddTransactionModal('expense')" class="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition">
                            <i class="fas fa-minus mr-2"></i>Agregar Gasto
                        </button>
                    </div>
                </div>
            `;
        }

        function getEstadisticasContent() {
            return `
                <div class="fade-in">
                    <!-- Period Selector -->
                    <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                        <div class="flex flex-wrap items-center justify-between gap-4">
                            <h3 class="font-semibold text-gray-800">Estadísticas Financieras</h3>
                            <div class="flex space-x-2">
                                <button onclick="loadStatsPeriod('week', this)" class="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition">
                                    Semana
                                </button>
                                <button onclick="loadStatsPeriod('month', this)" class="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg">
                                    Mes
                                </button>
                                <button onclick="loadStatsPeriod('year', this)" class="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition">
                                    Año
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- Expenses by Category Chart -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h4 class="font-medium text-gray-800 mb-4">Gastos por Categoría</h4>
                            <div class="relative h-64">
                                <canvas id="categoryChart"></canvas>
                            </div>
                        </div>

                        <!-- Monthly Trend Chart -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h4 class="font-medium text-gray-800 mb-4">Tendencia Mensual</h4>
                            <div class="relative h-64">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Summary -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        ${getStatsSummaryHTML()}
                    </div>

                    <!-- Top Transactions -->
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h4 class="font-medium text-gray-800 mb-4">Transacciones Más Grandes</h4>
                        <div class="space-y-3">
                            ${getTopTransactionsHTML()}
                        </div>
                    </div>
                </div>
            `;
        }

        function getStatsSummaryHTML() {
            const currentMonth = new Date().toISOString().slice(0, 7);
            const transactions = app.data.transactions.filter(t => t.date.startsWith(currentMonth));

            const totalIncome = transactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
            const totalExpenses = transactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);
            const avgTransaction = transactions.length > 0 ? (totalIncome + totalExpenses) / transactions.length : 0;
            const transactionCount = transactions.length;

            return `
                <div class="bg-green-50 p-4 rounded-lg text-center">
                    <p class="text-2xl font-bold text-green-600">${formatCLP(totalIncome)}</p>
                    <p class="text-sm text-green-800">Total Ingresos</p>
                </div>
                <div class="bg-red-50 p-4 rounded-lg text-center">
                    <p class="text-2xl font-bold text-red-600">${formatCLP(totalExpenses)}</p>
                    <p class="text-sm text-red-800">Total Gastos</p>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg text-center">
                    <p class="text-2xl font-bold text-blue-600">${formatCLP(avgTransaction)}</p>
                    <p class="text-sm text-blue-800">Promedio por Transacción</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg text-center">
                    <p class="text-2xl font-bold text-purple-600">${transactionCount}</p>
                    <p class="text-sm text-purple-800">Total Transacciones</p>
                </div>
            `;
        }

        function getTopTransactionsHTML() {
            const topTransactions = app.data.transactions
                .sort((a, b) => b.amount - a.amount)
                .slice(0, 5);

            if (topTransactions.length === 0) {
                return '<p class="text-gray-500 text-center py-4">No hay transacciones para mostrar</p>';
            }

            return topTransactions.map(transaction => {
                const isIncome = transaction.type === 'income';
                const bgClass = isIncome ? 'bg-green-100' : 'bg-red-100';
                const iconClass = isIncome ? 'fa-plus' : 'fa-minus';
                const textColorClass = isIncome ? 'text-green-600' : 'text-red-600';

                return `
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg mobile-card">
                        <div class="flex items-center space-x-3 flex-1">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center ${bgClass} flex-shrink-0">
                                <i class="fas ${iconClass} ${textColorClass}"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="font-medium text-gray-900 text-base truncate">${transaction.description}</p>
                                <p class="text-sm text-gray-500 truncate">${getCategoryLabel(transaction.category, transaction.type)} • ${formatDate(transaction.date)}</p>
                            </div>
                        </div>
                        <p class="font-bold ${textColorClass} text-base ml-4 flex-shrink-0 break-words">${formatCLP(transaction.amount)}</p>
                    </div>
                `;
            }).join('');
        }

        function loadStatsPeriod(period, clickedElement) {
            try {
                // Update button states
                document.querySelectorAll('[onclick^="loadStatsPeriod"]').forEach(btn => {
                    btn.className = 'px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition';
                });

                // Find the clicked button and update its state
                const clickedBtn = clickedElement || document.querySelector(`[onclick*="loadStatsPeriod('${period}')"]`);
                if (clickedBtn) {
                    clickedBtn.className = 'px-3 py-1 text-sm bg-blue-600 text-white rounded-lg';
                }

                const periodNames = {
                    'week': 'la semana',
                    'month': 'el mes',
                    'year': 'el año'
                };

                showNotification(`Cargando estadísticas de ${periodNames[period] || 'el período seleccionado'}`, 'info');

                // Update charts with new period data
                setTimeout(() => {
                    if (typeof Chart !== 'undefined') {
                        initializeCharts();
                    }
                }, 500);

            } catch (error) {
                console.error('Error loading stats period:', error);
                showNotification('Error al cargar estadísticas', 'error');
            }
        }

        function getCategoriasContent() {
            return `
                <div class="fade-in">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Gestión de Categorías</h3>
                        <p class="text-gray-600 mb-4">Personaliza las categorías de ingresos y gastos.</p>
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-medium text-gray-700 mb-2">Categorías de Gastos</h4>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">Alimentación</span>
                                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">Transporte</span>
                                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">Servicios Básicos</span>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-700 mb-2">Categorías de Ingresos</h4>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Sueldo</span>
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Freelance</span>
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Inversiones</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getPresupuestosContent() {
            return `
                <div class="fade-in">
                    <!-- Recurring Expenses Section -->
                    ${getRecurringExpensesSection()}

                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Gestión de Presupuestos</h3>
                        <p class="text-gray-600 mb-4">Establece límites de gasto por categoría y recibe alertas.</p>
                        <div class="space-y-4">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-medium">Alimentación</span>
                                    <span class="text-sm text-gray-500">$300.000 / $400.000</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-orange-500 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <p class="text-xs text-orange-600 mt-1">75% utilizado</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getMetasContent() {
            return `
                <div class="fade-in">
                    <!-- Add Goal Form -->
                    <div class="bg-white rounded-xl shadow-sm p-6 mb-6 mobile-card">
                        <h3 class="font-semibold text-gray-800 mb-6 text-lg">Crear Nueva Meta de Ahorro</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Nombre de la Meta</label>
                                <input type="text" id="goalName" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500" placeholder="Ej: Vacaciones, Auto nuevo">
                            </div>
                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Monto Objetivo ($)</label>
                                <input type="number" id="goalAmount" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500" placeholder="0">
                            </div>
                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Fecha Objetivo</label>
                                <input type="date" id="goalDate" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                            </div>
                            <div class="pt-4">
                                <button onclick="addSavingsGoal()" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-4 px-6 rounded-lg transition font-medium text-base mobile-action-btn">
                                    <i class="fas fa-bullseye mr-2"></i>Crear Meta
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Current Goals -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        ${getSavingsGoalsHTML()}
                    </div>

                    <!-- Savings Summary -->
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Resumen de Ahorros</h3>
                        ${getSavingsSummaryHTML()}
                    </div>
                </div>
            `;
        }

        function getSavingsGoalsHTML() {
            if (!app.data.savingsGoals || app.data.savingsGoals.length === 0) {
                return `
                    <div class="lg:col-span-2">
                        <div class="bg-gray-50 rounded-xl p-8 text-center">
                            <i class="fas fa-bullseye text-4xl text-gray-400 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-600 mb-2">No tienes metas de ahorro</h3>
                            <p class="text-gray-500">Crea tu primera meta para empezar a ahorrar con propósito</p>
                        </div>
                    </div>
                `;
            }

            return app.data.savingsGoals.map(goal => {
                const currentSavings = calculateCurrentSavings(goal);
                const progress = goal.targetAmount > 0 ? (currentSavings / goal.targetAmount) * 100 : 0;
                const remaining = goal.targetAmount - currentSavings;
                const daysLeft = Math.ceil((new Date(goal.targetDate) - new Date()) / (1000 * 60 * 60 * 24));
                const dailySavingsNeeded = daysLeft > 0 ? remaining / daysLeft : 0;

                return `
                    <div class="bg-white rounded-xl shadow-sm p-6 border-l-4 border-purple-500">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h4 class="text-lg font-semibold text-gray-800">${goal.name}</h4>
                                <p class="text-sm text-gray-500">Meta: ${formatCLP(goal.targetAmount)}</p>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="addToGoal(${goal.id})" class="bg-purple-100 text-purple-600 px-3 py-1 rounded-md text-sm hover:bg-purple-200 transition">
                                    <i class="fas fa-plus mr-1"></i>Agregar
                                </button>
                                <button onclick="deleteGoal(${goal.id})" class="bg-red-100 text-red-600 px-3 py-1 rounded-md text-sm hover:bg-red-200 transition">
                                    <i class="fas fa-trash mr-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>Progreso: ${progress.toFixed(1)}%</span>
                                <span>${formatCLP(currentSavings)} / ${formatCLP(goal.targetAmount)}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-purple-500 h-3 rounded-full transition-all duration-500" style="width: ${Math.min(progress, 100)}%"></div>
                            </div>
                        </div>

                        <!-- Goal Stats -->
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <p class="text-gray-500">Faltan</p>
                                <p class="font-medium ${remaining <= 0 ? 'text-purple-600' : 'text-gray-800'}">${remaining <= 0 ? '¡Meta alcanzada!' : formatCLP(remaining)}</p>
                            </div>
                            <div>
                                <p class="text-gray-500">Días restantes</p>
                                <p class="font-medium ${daysLeft <= 0 ? 'text-red-600' : 'text-gray-800'}">${daysLeft <= 0 ? 'Vencida' : daysLeft + ' días'}</p>
                            </div>
                            ${remaining > 0 && daysLeft > 0 ? `
                                <div class="col-span-2">
                                    <p class="text-gray-500">Ahorro diario necesario</p>
                                    <p class="font-medium text-purple-600">${formatCLP(dailySavingsNeeded)}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getSavingsSummaryHTML() {
            if (!app.data.savingsGoals || app.data.savingsGoals.length === 0) {
                return '<p class="text-gray-500">No hay metas para mostrar resumen</p>';
            }

            const totalGoals = app.data.savingsGoals.length;
            const totalTargetAmount = app.data.savingsGoals.reduce((sum, goal) => sum + goal.targetAmount, 0);
            const totalCurrentSavings = app.data.savingsGoals.reduce((sum, goal) => sum + calculateCurrentSavings(goal), 0);
            const completedGoals = app.data.savingsGoals.filter(goal => calculateCurrentSavings(goal) >= goal.targetAmount).length;
            const overallProgress = totalTargetAmount > 0 ? (totalCurrentSavings / totalTargetAmount) * 100 : 0;

            return `
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center p-4 bg-purple-50 rounded-lg">
                        <p class="text-2xl font-bold text-purple-600">${totalGoals}</p>
                        <p class="text-sm text-purple-800">Metas Totales</p>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <p class="text-2xl font-bold text-green-600">${completedGoals}</p>
                        <p class="text-sm text-green-800">Completadas</p>
                    </div>
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <p class="text-2xl font-bold text-blue-600">${formatCLP(totalCurrentSavings)}</p>
                        <p class="text-sm text-blue-800">Total Ahorrado</p>
                    </div>
                    <div class="text-center p-4 bg-orange-50 rounded-lg">
                        <p class="text-2xl font-bold text-orange-600">${overallProgress.toFixed(1)}%</p>
                        <p class="text-sm text-orange-800">Progreso General</p>
                    </div>
                </div>
            `;
        }

        function calculateCurrentSavings(goal) {
            // Calculate savings based on transactions since goal creation
            const goalDate = new Date(goal.createdAt);
            const savingsTransactions = app.data.transactions.filter(t =>
                t.type === 'income' &&
                new Date(t.date) >= goalDate &&
                t.description.toLowerCase().includes(goal.name.toLowerCase())
            );

            return savingsTransactions.reduce((sum, t) => sum + t.amount, 0);
        }

        function addSavingsGoal() {
            const name = document.getElementById('goalName').value.trim();
            const amount = parseFloat(document.getElementById('goalAmount').value);
            const date = document.getElementById('goalDate').value;

            if (!name) {
                showNotification('Por favor ingrese un nombre para la meta', 'error');
                return;
            }

            if (!amount || amount <= 0) {
                showNotification('Por favor ingrese un monto válido', 'error');
                return;
            }

            if (!date) {
                showNotification('Por favor seleccione una fecha objetivo', 'error');
                return;
            }

            const goal = {
                id: Date.now(),
                name: name,
                targetAmount: amount,
                targetDate: date,
                createdAt: new Date().toISOString(),
                completed: false
            };

            app.data.savingsGoals.push(goal);
            saveData();

            // Clear form
            document.getElementById('goalName').value = '';
            document.getElementById('goalAmount').value = '';
            document.getElementById('goalDate').value = '';

            loadSection('metas');
            vibrate(100);
            showNotification(`Meta "${name}" creada exitosamente`, 'success');
        }

        function addToGoal(goalId) {
            const goal = app.data.savingsGoals.find(g => g.id === goalId);
            if (!goal) return;

            const amount = prompt(`¿Cuánto quieres agregar a "${goal.name}"?`);
            if (!amount || isNaN(amount) || parseFloat(amount) <= 0) return;

            const transaction = {
                id: Date.now(),
                type: 'income',
                category: 'other',
                amount: parseFloat(amount),
                description: `Ahorro para ${goal.name}`,
                date: new Date().toISOString().split('T')[0]
            };

            app.data.transactions.unshift(transaction);
            saveData();
            loadSection('metas');
            vibrate(100);
            showNotification(`${formatCLP(parseFloat(amount))} agregado a "${goal.name}"`, 'success');
        }

        function deleteGoal(goalId) {
            const goal = app.data.savingsGoals.find(g => g.id === goalId);
            if (!goal) return;

            if (confirm(`¿Estás seguro de eliminar la meta "${goal.name}"?`)) {
                app.data.savingsGoals = app.data.savingsGoals.filter(g => g.id !== goalId);
                saveData();
                loadSection('metas');
                showNotification('Meta eliminada', 'success');
            }
        }

        function getRecurrentesContent() {
            return `
                <div class="fade-in">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="font-semibold text-gray-800 mb-4">Gastos Recurrentes</h3>
                        <p class="text-gray-600 mb-4">Automatiza el registro de gastos que se repiten mensualmente.</p>
                        <button onclick="showCreateRecurringModal()" class="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition">
                            <i class="fas fa-redo mr-2"></i>Agregar Gasto Recurrente
                        </button>
                        <div class="mt-6 space-y-4">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <span class="font-medium">Arriendo</span>
                                        <p class="text-sm text-gray-500">Mensual • Próximo pago: 01/02/2024</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="font-bold text-orange-600">$450.000</span>
                                        <p class="text-xs text-gray-500">5 días restantes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getAjustesContent() {
            return `
                <div class="fade-in">
                    <div class="space-y-6">
                        <!-- Theme Selection -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="font-semibold text-gray-800 mb-4">Personalización</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Tema de Color</label>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                        <button onclick="changeTheme('default')" class="p-3 border-2 border-blue-500 rounded-lg bg-blue-500 text-white">
                                            Azul
                                        </button>
                                        <button onclick="changeTheme('green')" class="p-3 border-2 border-gray-200 rounded-lg bg-green-500 text-white">
                                            Verde
                                        </button>
                                        <button onclick="changeTheme('purple')" class="p-3 border-2 border-gray-200 rounded-lg bg-purple-500 text-white">
                                            Púrpura
                                        </button>
                                        <button onclick="changeTheme('orange')" class="p-3 border-2 border-gray-200 rounded-lg bg-orange-500 text-white">
                                            Naranja
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Family Settings -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="font-semibold text-gray-800 mb-4">Configuración Familiar</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Nombre de la Familia</label>
                                    <input type="text" id="familyNameInput" value="${app.data.familyName}" class="w-full border border-gray-200 rounded-lg px-3 py-2">
                                </div>
                                <button onclick="updateFamilyName()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                    Actualizar
                                </button>
                            </div>
                        </div>

                        <!-- Data Management -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="font-semibold text-gray-800 mb-4">Gestión de Datos</h3>
                            <div class="space-y-3">
                                <button onclick="exportData()" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">
                                    <i class="fas fa-download mr-2"></i>Exportar Datos
                                </button>
                                <button onclick="backupToCloud()" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                                    <i class="fas fa-cloud-upload-alt mr-2"></i>Backup en la Nube
                                </button>
                                <button onclick="clearAllData()" class="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700">
                                    <i class="fas fa-trash mr-2"></i>Limpiar Todos los Datos
                                </button>
                            </div>
                        </div>

                        <!-- App Info -->
                        <div class="bg-white rounded-xl shadow-sm p-6">
                            <h3 class="font-semibold text-gray-800 mb-4">Información de la App</h3>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-600">Versión:</p>
                                    <p class="font-medium">3.0.0 Pro</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">Transacciones:</p>
                                    <p class="font-medium">${app.data.transactions.length}</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">Plataforma:</p>
                                    <p class="font-medium">${isRunningInApp() ? '📱 App Android' : '🌐 Navegador'}</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">Modo:</p>
                                    <p class="font-medium">🔒 Offline Ready</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Additional utility functions
        function vibrate(duration = 100) {
            if ('vibrate' in navigator) {
                navigator.vibrate(duration);
            }
        }

        function isRunningInApp() {
            return (
                window.navigator.standalone ||
                window.matchMedia('(display-mode: standalone)').matches ||
                document.referrer.includes('android-app://') ||
                window.location.protocol === 'file:' ||
                navigator.userAgent.includes('wv')
            );
        }

        function changeTheme(theme) {
            app.state.currentTheme = theme;
            document.body.className = theme !== 'default' ? 'theme-' + theme : '';
            localStorage.setItem('theme', theme);
            showNotification('Tema actualizado', 'success');
        }

        function updateFamilyName() {
            const input = document.getElementById('familyNameInput');
            if (input && input.value.trim()) {
                app.data.familyName = input.value.trim();
                localStorage.setItem('familyName', app.data.familyName);
                const familyNameEl = document.getElementById('familyName');
                if (familyNameEl) familyNameEl.textContent = app.data.familyName;
                showNotification('Nombre de familia actualizado', 'success');
            }
        }

        function exportData() {
            const dataStr = JSON.stringify(app.data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'famifinanzas-backup.json';
            link.click();
            showNotification('Datos exportados exitosamente', 'success');
        }

        function backupToCloud() {
            showNotification('Preparando backup...', 'info');
            setTimeout(() => {
                const backupData = {
                    timestamp: new Date().toISOString(),
                    data: app.data,
                    version: '3.0.0'
                };
                localStorage.setItem('cloud_backup', JSON.stringify(backupData));
                vibrate(200);
                showNotification('Backup guardado en la nube ☁️', 'success');
            }, 2000);
        }

        function clearAllData() {
            if (confirm('¿Está seguro de que desea eliminar TODOS los datos? Esta acción no se puede deshacer.')) {
                app.data.transactions = [];
                app.data.customCategories = { income: [], expense: [] };
                app.data.budgets = {};
                app.data.savingsGoals = [];
                app.data.recurringExpenses = [];
                app.data.insights = { lastAnalysis: null, suggestions: [] };
                saveData();
                loadSection(app.state.currentSection);
                showNotification('Todos los datos han sido eliminados', 'success');
            }
        }

        // Transaction Modal Functions
        function showAddTransactionModal(type) {
            const modal = document.getElementById('transactionModal');
            const modalTitle = document.getElementById('modalTitle');
            const transactionType = document.getElementById('transactionType');
            const transactionDate = document.getElementById('transactionDate');

            // Set modal title and type
            modalTitle.textContent = type === 'income' ? 'Agregar Ingreso' : 'Agregar Gasto';
            transactionType.value = type;

            // Set today's date as default
            transactionDate.value = new Date().toISOString().split('T')[0];

            // Populate categories
            populateCategories(type);

            // Show modal
            modal.classList.remove('hidden');

            // Focus on description field
            setTimeout(() => {
                document.getElementById('transactionDescription').focus();
            }, 100);
        }

        function closeTransactionModal() {
            const modal = document.getElementById('transactionModal');
            modal.classList.add('hidden');

            // Reset form
            document.getElementById('transactionForm').reset();
        }

        function populateCategories(type) {
            const categorySelect = document.getElementById('transactionCategory');
            const categories = {
                expense: [
                    { value: 'food', label: 'Alimentación' },
                    { value: 'transport', label: 'Transporte' },
                    { value: 'utilities', label: 'Servicios Básicos' },
                    { value: 'entertainment', label: 'Entretenimiento' },
                    { value: 'health', label: 'Salud' },
                    { value: 'education', label: 'Educación' },
                    { value: 'clothing', label: 'Vestuario' },
                    { value: 'home', label: 'Hogar' },
                    { value: 'other', label: 'Otros' }
                ],
                income: [
                    { value: 'salary', label: 'Sueldo' },
                    { value: 'freelance', label: 'Trabajos Independientes' },
                    { value: 'investment', label: 'Inversiones' },
                    { value: 'bonus', label: 'Bonos' },
                    { value: 'other', label: 'Otros' }
                ]
            };

            categorySelect.innerHTML = '';
            categories[type].forEach(cat => {
                const option = document.createElement('option');
                option.value = cat.value;
                option.textContent = cat.label;
                categorySelect.appendChild(option);
            });
        }

        function handleTransactionSubmit(event) {
            event.preventDefault();

            const type = document.getElementById('transactionType').value;
            const description = sanitizeHTML(document.getElementById('transactionDescription').value.trim());
            const category = document.getElementById('transactionCategory').value;
            const amount = parseFloat(document.getElementById('transactionAmount').value);
            const date = document.getElementById('transactionDate').value;

            // Enhanced validation
            if (!description) {
                showNotification('Por favor ingrese una descripción', 'error');
                return;
            }

            if (description.length > 100) {
                showNotification('La descripción no puede exceder 100 caracteres', 'error');
                return;
            }

            if (!amount || amount <= 0) {
                showNotification('Por favor ingrese un monto válido', 'error');
                return;
            }

            if (amount > 999999999) {
                showNotification('El monto es demasiado grande', 'error');
                return;
            }

            if (!date) {
                showNotification('Por favor seleccione una fecha', 'error');
                return;
            }

            // Validate date is not in the future beyond reasonable limits
            const selectedDate = new Date(date);
            const today = new Date();
            const maxFutureDate = new Date();
            maxFutureDate.setFullYear(today.getFullYear() + 1);

            if (selectedDate > maxFutureDate) {
                showNotification('La fecha no puede ser más de un año en el futuro', 'error');
                return;
            }

            // Create transaction
            const transaction = {
                id: Date.now(),
                type: type,
                description: description,
                category: category,
                amount: amount,
                date: date
            };

            // Add to data
            app.data.transactions.unshift(transaction);

            // Save data
            saveData();

            // Close modal
            closeTransactionModal();

            // Refresh current section
            loadSection(app.state.currentSection);

            // Show success notification
            vibrate(100);
            showNotification(`${type === 'income' ? 'Ingreso' : 'Gasto'} agregado exitosamente`, 'success');
        }

        function showCreateGoalModal() {
            // Switch to goals section and pre-fill form
            loadSection('metas');

            // Wait for section to load, then focus on form
            setTimeout(() => {
                const goalNameInput = document.getElementById('goalName');
                const goalAmountInput = document.getElementById('goalAmount');

                if (goalNameInput && goalAmountInput) {
                    // Pre-fill with suggested values
                    goalNameInput.value = 'Meta de Ahorro Sugerida';
                    goalNameInput.focus();

                    // Scroll to form
                    goalNameInput.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    showNotification('Formulario de meta preparado', 'success');
                    vibrate(50);
                }
            }, 500);
        }

        function showCreateRecurringModal() {
            const modalHtml = `
                <div id="recurringModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div class="bg-white rounded-xl p-6 w-full max-w-md mx-auto modal">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Crear Gasto Recurrente</h3>
                            <button onclick="closeRecurringModal()" class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition">
                                <i class="fas fa-times text-lg"></i>
                            </button>
                        </div>

                        <form id="recurringForm" class="space-y-6">
                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Descripción</label>
                                <input type="text" id="recurringDescription" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none" placeholder="Ej: Arriendo, Internet, Luz" required>
                            </div>

                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Categoría</label>
                                <select id="recurringCategory" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none">
                                    <option value="servicios-basicos">Servicios Básicos</option>
                                    <option value="vivienda">Vivienda</option>
                                    <option value="transporte">Transporte</option>
                                    <option value="seguros">Seguros</option>
                                    <option value="suscripciones">Suscripciones</option>
                                    <option value="otros">Otros</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Monto ($)</label>
                                <input type="number" id="recurringAmount" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none" placeholder="0" required>
                            </div>

                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Frecuencia</label>
                                <select id="recurringFrequency" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none">
                                    <option value="monthly">Mensual</option>
                                    <option value="weekly">Semanal</option>
                                    <option value="biweekly">Quincenal</option>
                                    <option value="quarterly">Trimestral</option>
                                    <option value="yearly">Anual</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-base font-medium text-gray-700 mb-2">Próximo Vencimiento</label>
                                <input type="date" id="recurringNextDate" class="w-full border-2 border-gray-200 rounded-lg px-4 py-3 text-base focus:border-blue-500 focus:outline-none" required>
                            </div>

                            <div class="flex space-x-4 pt-6">
                                <button type="button" onclick="closeRecurringModal()" class="flex-1 bg-gray-200 text-gray-800 py-3 px-6 rounded-lg hover:bg-gray-300 transition font-medium text-base">
                                    Cancelar
                                </button>
                                <button type="submit" class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition font-medium text-base">
                                    Crear Recurrente
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Set default next date to next month
            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            document.getElementById('recurringNextDate').value = nextMonth.toISOString().split('T')[0];

            // Add form submit handler
            document.getElementById('recurringForm').addEventListener('submit', handleRecurringSubmit);

            // Focus on first input
            setTimeout(() => {
                document.getElementById('recurringDescription').focus();
            }, 100);
        }

        function closeRecurringModal() {
            const modal = document.getElementById('recurringModal');
            if (modal) {
                modal.remove();
            }
        }

        function handleRecurringSubmit(e) {
            e.preventDefault();

            const formData = {
                description: document.getElementById('recurringDescription').value,
                category: document.getElementById('recurringCategory').value,
                amount: parseFloat(document.getElementById('recurringAmount').value),
                frequency: document.getElementById('recurringFrequency').value,
                nextDate: document.getElementById('recurringNextDate').value
            };

            if (!formData.description || !formData.amount || formData.amount <= 0) {
                showNotification('Por favor complete todos los campos requeridos', 'error');
                return;
            }

            // Initialize recurring expenses array if it doesn't exist
            if (!app.data.recurringExpenses) {
                app.data.recurringExpenses = [];
            }

            // Add recurring expense
            const recurringExpense = {
                id: Date.now().toString(),
                ...formData,
                createdAt: new Date().toISOString(),
                isActive: true
            };

            app.data.recurringExpenses.push(recurringExpense);
            saveData();

            closeRecurringModal();
            showNotification('Gasto recurrente creado exitosamente', 'success');
            vibrate(100);

            // Refresh current section if it's presupuestos
            if (app.state.currentSection === 'presupuestos') {
                loadSection('presupuestos');
            }
        }

        // Currency conversion functions
        function convertCurrency() {
            const amount = parseFloat(document.getElementById('convertAmount').value);
            const convertTo = document.getElementById('convertTo').value;
            const resultDiv = document.getElementById('conversionResult');

            if (!amount || amount <= 0) {
                showNotification('Por favor ingrese un monto válido', 'error');
                return;
            }

            if (!app.currencyRates) {
                showNotification('Cargando tasas de cambio...', 'info');
                return;
            }

            let result;
            let symbol;

            switch(convertTo) {
                case 'uf':
                    result = amount / app.currencyRates.uf;
                    symbol = 'UF';
                    break;
                case 'usd':
                    result = amount / app.currencyRates.usd;
                    symbol = 'USD';
                    break;
                case 'eur':
                    result = amount / app.currencyRates.eur;
                    symbol = 'EUR';
                    break;
                default:
                    showNotification('Moneda no válida', 'error');
                    return;
            }

            resultDiv.innerHTML = `
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p class="text-blue-800">
                        <strong>${formatCLP(amount)}</strong> =
                        <strong>${result.toFixed(convertTo === 'uf' ? 4 : 2)} ${symbol}</strong>
                    </p>
                </div>
            `;

            vibrate(50);
        }

        function searchPrices() {
            const searchTerm = document.getElementById('searchProduct').value.trim();
            const resultsDiv = document.getElementById('priceResults');

            if (!searchTerm) {
                showNotification('Por favor ingrese un producto para buscar', 'error');
                return;
            }

            // Show loading state with source information
            resultsDiv.innerHTML = `
                <div class="text-center py-6">
                    <i class="fas fa-spinner fa-spin text-blue-600 text-2xl mb-3"></i>
                    <p class="text-gray-700 font-medium">Buscando precios en tiendas online...</p>
                    <p class="text-sm text-gray-500 mt-2">Consultando: Jumbo, Lider, Santa Isabel, Unimarc, Tottus</p>
                    <div class="mt-4 bg-blue-50 p-3 rounded-lg">
                        <p class="text-xs text-blue-600">
                            <i class="fas fa-info-circle mr-1"></i>
                            Los precios se obtienen de los sitios web oficiales de cada supermercado
                        </p>
                    </div>
                </div>
            `;

            // Simulate API call with realistic Chilean supermarket data
            const timeoutId = setTimeout(() => {
                const mockResults = generateMockPriceResults(searchTerm);
                displayPriceResults(mockResults, resultsDiv);

                // Add source information footer
                const sourceInfo = `
                    <div class="mt-4 bg-gray-50 p-4 rounded-lg border">
                        <h4 class="font-medium text-gray-800 mb-2">
                            <i class="fas fa-database mr-2"></i>
                            Fuentes de Información
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                            <div class="flex items-center space-x-2">
                                <span class="text-red-600">🛒</span>
                                <a href="https://www.jumbo.cl" target="_blank" class="text-blue-600 hover:underline">Jumbo.cl</a>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-blue-600">🏪</span>
                                <a href="https://www.lider.cl" target="_blank" class="text-blue-600 hover:underline">Lider.cl</a>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-green-600">🏬</span>
                                <a href="https://www.santaisabel.cl" target="_blank" class="text-blue-600 hover:underline">SantaIsabel.cl</a>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-purple-600">🛍️</span>
                                <a href="https://www.unimarc.cl" target="_blank" class="text-blue-600 hover:underline">Unimarc.cl</a>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-orange-600">🏢</span>
                                <a href="https://www.tottus.cl" target="_blank" class="text-blue-600 hover:underline">Tottus.cl</a>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-3">
                            <i class="fas fa-clock mr-1"></i>
                            Precios actualizados: ${new Date().toLocaleString('es-CL')}
                        </p>
                    </div>
                `;

                resultsDiv.innerHTML += sourceInfo;

                vibrate(50);
                showNotification(`Encontrados ${mockResults.length} resultados para "${searchTerm}"`, 'success');
            }, 1500);

            addTimer('timeout', timeoutId);
        }

        function generateMockPriceResults(searchTerm) {
            const supermarkets = [
                {
                    name: 'Jumbo',
                    logo: '🛒',
                    color: 'text-red-600',
                    website: 'https://www.jumbo.cl',
                    searchUrl: 'https://www.jumbo.cl/buscar?q='
                },
                {
                    name: 'Lider',
                    logo: '🏪',
                    color: 'text-blue-600',
                    website: 'https://www.lider.cl',
                    searchUrl: 'https://www.lider.cl/supermercado/search?Ntt='
                },
                {
                    name: 'Santa Isabel',
                    logo: '🏬',
                    color: 'text-green-600',
                    website: 'https://www.santaisabel.cl',
                    searchUrl: 'https://www.santaisabel.cl/buscar?q='
                },
                {
                    name: 'Unimarc',
                    logo: '🛍️',
                    color: 'text-purple-600',
                    website: 'https://www.unimarc.cl',
                    searchUrl: 'https://www.unimarc.cl/buscar?q='
                },
                {
                    name: 'Tottus',
                    logo: '🏢',
                    color: 'text-orange-600',
                    website: 'https://www.tottus.cl',
                    searchUrl: 'https://www.tottus.cl/tottus/search/?text='
                }
            ];

            // Generate realistic prices based on search term
            const basePrice = getBasePriceForProduct(searchTerm);

            return supermarkets.map(store => ({
                store: store.name,
                logo: store.logo,
                color: store.color,
                website: store.website,
                searchUrl: store.searchUrl + encodeURIComponent(searchTerm),
                price: Math.round(basePrice * (0.85 + Math.random() * 0.3)),
                discount: Math.random() > 0.6 ? Math.round(Math.random() * 20 + 5) : 0,
                availability: Math.random() > 0.2 ? 'Disponible' : 'Stock limitado',
                deliveryTime: getRandomDeliveryTime(),
                freeShipping: Math.random() > 0.5
            })).sort((a, b) => a.price - b.price);
        }

        function getRandomDeliveryTime() {
            const options = [
                'Entrega hoy',
                'Entrega mañana',
                '2-3 días hábiles',
                '3-5 días hábiles',
                'Retiro en tienda'
            ];
            return options[Math.floor(Math.random() * options.length)];
        }

        function getBasePriceForProduct(product) {
            const productPrices = {
                'leche': 1200,
                'pan': 800,
                'arroz': 1500,
                'aceite': 2500,
                'azucar': 1000,
                'fideos': 900,
                'pollo': 3500,
                'carne': 8000,
                'pescado': 4500,
                'huevos': 2200,
                'queso': 3800,
                'yogurt': 1800,
                'manzana': 2000,
                'platano': 1500,
                'tomate': 2500,
                'cebolla': 1200,
                'papa': 1000,
                'detergente': 3500,
                'shampoo': 4000,
                'pasta dental': 2800
            };

            const lowerProduct = product.toLowerCase();
            for (const [key, price] of Object.entries(productPrices)) {
                if (lowerProduct.includes(key)) {
                    return price;
                }
            }

            // Default price for unknown products
            return 2000 + Math.random() * 3000;
        }

        function displayPriceResults(results, container) {
            if (results.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-4">No se encontraron resultados</p>';
                return;
            }

            const html = results.map((result, index) => `
                <div class="bg-white rounded-lg shadow-sm border ${index === 0 ? 'border-green-200 bg-green-50' : 'border-gray-200'} mb-4 overflow-hidden">
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">${result.logo}</span>
                                <div>
                                    <p class="font-medium ${result.color} text-lg">${result.store}</p>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="text-xs ${result.availability === 'Disponible' ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'} px-2 py-1 rounded-full">
                                            ${result.availability}
                                        </span>
                                        ${result.discount > 0 ? `<span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">-${result.discount}% OFF</span>` : ''}
                                        ${result.freeShipping ? '<span class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">Envío gratis</span>' : ''}
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-xl ${index === 0 ? 'text-green-600' : 'text-gray-800'}">${formatCLP(result.price)}</p>
                                ${index === 0 ? '<p class="text-xs text-green-600 font-medium">🏆 Mejor precio</p>' : ''}
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-truck mr-1"></i>
                                ${result.deliveryTime}
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="openStoreSearch('${result.searchUrl}')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition flex items-center space-x-1">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Comprar</span>
                                </button>
                                <button onclick="openStoreWebsite('${result.website}')" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm transition">
                                    <i class="fas fa-external-link-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    ${index === 0 ? `
                        <div class="bg-green-100 px-4 py-2 border-t border-green-200">
                            <p class="text-green-700 text-sm font-medium">
                                <i class="fas fa-star mr-1"></i>
                                Recomendado: Mejor relación precio-calidad
                            </p>
                        </div>
                    ` : ''}
                </div>
            `).join('');

            container.innerHTML = html;
        }

        function openStoreSearch(url) {
            if (confirm('¿Deseas ir al sitio web de la tienda para comprar este producto?')) {
                window.open(url, '_blank', 'noopener,noreferrer');
                showNotification('Abriendo tienda online...', 'info');
                vibrate(50);
            }
        }

        function openStoreWebsite(url) {
            if (confirm('¿Deseas visitar el sitio web de la tienda?')) {
                window.open(url, '_blank', 'noopener,noreferrer');
                showNotification('Abriendo sitio web...', 'info');
                vibrate(50);
            }
        }

        function showPriceComparatorInfo() {
            const modalHtml = `
                <div id="priceInfoModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div class="bg-white rounded-xl p-6 w-full max-w-lg mx-auto modal">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                Comparador de Precios
                            </h3>
                            <button onclick="closePriceInfoModal()" class="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition">
                                <i class="fas fa-times text-lg"></i>
                            </button>
                        </div>

                        <div class="space-y-4">
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-medium text-green-800 mb-2">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    ¿Cómo funciona?
                                </h4>
                                <ul class="text-sm text-green-700 space-y-1">
                                    <li>• Busca productos en 5 supermercados principales</li>
                                    <li>• Compara precios en tiempo real</li>
                                    <li>• Muestra descuentos y ofertas especiales</li>
                                    <li>• Enlaces directos para comprar online</li>
                                </ul>
                            </div>

                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2">
                                    <i class="fas fa-store mr-2"></i>
                                    Tiendas incluidas:
                                </h4>
                                <div class="grid grid-cols-2 gap-2 text-sm text-blue-700">
                                    <div class="flex items-center space-x-2">
                                        <span>🛒</span>
                                        <span>Jumbo</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span>🏪</span>
                                        <span>Lider</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span>🏬</span>
                                        <span>Santa Isabel</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span>🛍️</span>
                                        <span>Unimarc</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span>🏢</span>
                                        <span>Tottus</span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <h4 class="font-medium text-yellow-800 mb-2">
                                    <i class="fas fa-lightbulb mr-2"></i>
                                    Consejos de uso:
                                </h4>
                                <ul class="text-sm text-yellow-700 space-y-1">
                                    <li>• Usa términos específicos (ej: "leche entera 1L")</li>
                                    <li>• Revisa los tiempos de entrega</li>
                                    <li>• Considera los costos de envío</li>
                                    <li>• Aprovecha las ofertas y descuentos</li>
                                </ul>
                            </div>

                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-xs text-gray-600">
                                    <i class="fas fa-shield-alt mr-1"></i>
                                    Los enlaces te llevan directamente a los sitios oficiales de cada supermercado para una compra segura.
                                </p>
                            </div>
                        </div>

                        <div class="mt-6">
                            <button onclick="closePriceInfoModal()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition font-medium">
                                Entendido
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closePriceInfoModal() {
            const modal = document.getElementById('priceInfoModal');
            if (modal) {
                modal.remove();
            }
        }

        function loadNationalData() {
            try {
                showNotification('Cargando datos económicos de Chile...', 'info');

                // Simulate loading Chilean economic data
                const timeoutId = setTimeout(() => {
                    try {
                        const nationalData = {
                            inflation: {
                                monthly: 0.3,
                                annual: 4.2,
                                trend: 'stable'
                            },
                            minimumWage: 380000,
                            averageIncome: 620000,
                            unemploymentRate: 7.8,
                            economicGrowth: 2.1,
                            lastUpdated: new Date().toLocaleDateString('es-CL')
                        };

                        displayNationalData(nationalData);
                        showNotification('Datos económicos actualizados', 'success');
                        vibrate(50);
                    } catch (error) {
                        console.error('Error displaying national data:', error);
                        showNotification('Error al cargar datos económicos', 'error');
                    }
                }, 2000);

                addTimer('timeout', timeoutId);
            } catch (error) {
                console.error('Error loading national data:', error);
                showNotification('Error al cargar datos económicos', 'error');
            }
        }

        function displayNationalData(data) {
            try {
                // Find all containers and update them
                const containers = document.querySelectorAll('#nationalDataContainer');
                if (containers.length === 0) {
                    console.error('No nationalDataContainer found');
                    return;
                }

                console.log(`Found ${containers.length} national data containers`);

                const html = `
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <h3 class="font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-flag text-red-600 mr-2"></i>
                        Indicadores Económicos de Chile
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-blue-600 font-medium">Inflación Anual</p>
                                    <p class="text-2xl font-bold text-blue-800">${data.inflation.annual}%</p>
                                </div>
                                <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                            </div>
                        </div>

                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-green-600 font-medium">Sueldo Mínimo</p>
                                    <p class="text-2xl font-bold text-green-800">${formatCLP(data.minimumWage)}</p>
                                </div>
                                <i class="fas fa-coins text-green-600 text-xl"></i>
                            </div>
                        </div>

                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-purple-600 font-medium">Ingreso Promedio</p>
                                    <p class="text-2xl font-bold text-purple-800">${formatCLP(data.averageIncome)}</p>
                                </div>
                                <i class="fas fa-wallet text-purple-600 text-xl"></i>
                            </div>
                        </div>

                        <div class="bg-orange-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-orange-600 font-medium">Desempleo</p>
                                    <p class="text-2xl font-bold text-orange-800">${data.unemploymentRate}%</p>
                                </div>
                                <i class="fas fa-briefcase text-orange-600 text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-800 mb-2">Comparación Personal</h4>
                        <div class="space-y-2">
                            ${generatePersonalComparison(data)}
                        </div>
                        <p class="text-xs text-gray-500 mt-3">Última actualización: ${data.lastUpdated}</p>
                    </div>
                </div>
                `;

                // Update all containers
                containers.forEach(container => {
                    safeSetInnerHTML(container, html);
                });

                console.log('National data displayed successfully');
            } catch (error) {
                console.error('Error displaying national data:', error);
                showNotification('Error al mostrar datos económicos', 'error');
            }
        }

        function generatePersonalComparison(nationalData) {
            const currentMonth = new Date().toISOString().slice(0, 7);
            const monthlyIncome = app.data.transactions
                .filter(t => t.type === 'income' && t.date.startsWith(currentMonth))
                .reduce((sum, t) => sum + t.amount, 0);

            let comparison = '';

            if (monthlyIncome > 0) {
                const vsAverage = ((monthlyIncome - nationalData.averageIncome) / nationalData.averageIncome * 100).toFixed(1);
                const vsMinimum = ((monthlyIncome - nationalData.minimumWage) / nationalData.minimumWage * 100).toFixed(1);

                comparison += `
                    <p class="text-sm">
                        <span class="font-medium">Tus ingresos:</span> ${formatCLP(monthlyIncome)}
                        <span class="text-${vsAverage >= 0 ? 'green' : 'red'}-600">
                            (${vsAverage >= 0 ? '+' : ''}${vsAverage}% vs promedio nacional)
                        </span>
                    </p>
                    <p class="text-sm">
                        <span class="font-medium">Vs sueldo mínimo:</span>
                        <span class="text-${vsMinimum >= 0 ? 'green' : 'red'}-600">
                            ${vsMinimum >= 0 ? '+' : ''}${vsMinimum}%
                        </span>
                    </p>
                `;
            } else {
                comparison = '<p class="text-sm text-gray-600">Registra ingresos para ver comparaciones personalizadas</p>';
            }

            return comparison;
        }

        function enableLocationTracking() {
            // Check if geolocation is supported
            if (!navigator.geolocation) {
                showNotification('Tu dispositivo no soporta geolocalización', 'error');
                console.error('Geolocation not supported by this browser');
                return;
            }

            // Show loading state
            showNotification('Solicitando permisos de ubicación...', 'info');

            // Clear any previous location data to ensure fresh data
            app.data.userLocation = null;

            // Configure geolocation options for maximum accuracy
            const geoOptions = {
                enableHighAccuracy: true,    // Use GPS if available
                timeout: 15000,              // 15 seconds timeout
                maximumAge: 60000            // Accept cached position up to 1 minute old
            };

            console.log('Requesting geolocation with options:', geoOptions);

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    try {
                        console.log('Geolocation success:', position);

                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        const accuracy = position.coords.accuracy;

                        // Validate coordinates
                        if (!isValidCoordinate(lat, lng)) {
                            throw new Error('Invalid coordinates received');
                        }

                        // Store real location data
                        app.data.userLocation = {
                            latitude: lat,
                            longitude: lng,
                            accuracy: accuracy,
                            timestamp: new Date().toISOString(),
                            altitude: position.coords.altitude,
                            altitudeAccuracy: position.coords.altitudeAccuracy,
                            heading: position.coords.heading,
                            speed: position.coords.speed
                        };

                        console.log('User location stored:', app.data.userLocation);

                        // Save to localStorage
                        saveData();

                        // Show location info to user
                        showLocationInfo(lat, lng, accuracy);

                        // Find real nearby stores based on actual coordinates
                        findRealNearbyStores(lat, lng);

                        vibrate(100);
                        showNotification(`Ubicación detectada con precisión de ${Math.round(accuracy)}m`, 'success');

                    } catch (error) {
                        console.error('Error processing location:', error);
                        showNotification('Error al procesar la ubicación obtenida', 'error');

                        // Fallback to demo mode with user consent
                        showLocationFallbackOptions();
                    }
                },
                (error) => {
                    console.error('Geolocation error:', error);

                    let message = 'Error al obtener ubicación';
                    let suggestion = '';

                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            message = 'Permisos de ubicación denegados';
                            suggestion = 'Por favor, permite el acceso a la ubicación en la configuración de tu navegador';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            message = 'Ubicación no disponible';
                            suggestion = 'Verifica que el GPS esté activado y tengas conexión';
                            break;
                        case error.TIMEOUT:
                            message = 'Tiempo de espera agotado';
                            suggestion = 'La búsqueda de ubicación tardó demasiado. Intenta nuevamente';
                            break;
                        default:
                            message = 'Error desconocido de geolocalización';
                            suggestion = 'Verifica la configuración de ubicación de tu dispositivo';
                    }

                    showNotification(message, 'error');

                    // Show detailed error information
                    showLocationErrorDetails(error, message, suggestion);
                },
                geoOptions
            );
        }

        function isValidCoordinate(lat, lng) {
            return (
                typeof lat === 'number' &&
                typeof lng === 'number' &&
                lat >= -90 && lat <= 90 &&
                lng >= -180 && lng <= 180 &&
                !isNaN(lat) && !isNaN(lng)
            );
        }

        function showLocationInfo(lat, lng, accuracy) {
            const container = document.getElementById('nearbyStores');
            if (!container) return;

            const locationInfo = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <h4 class="font-medium text-green-800 mb-2">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        Ubicación Detectada
                    </h4>
                    <div class="text-sm text-green-700 space-y-1">
                        <p><strong>Latitud:</strong> ${lat.toFixed(6)}</p>
                        <p><strong>Longitud:</strong> ${lng.toFixed(6)}</p>
                        <p><strong>Precisión:</strong> ±${Math.round(accuracy)} metros</p>
                        <p><strong>Detectado:</strong> ${new Date().toLocaleString('es-CL')}</p>
                    </div>
                    <button onclick="openCurrentLocationInMaps(${lat}, ${lng})" class="mt-3 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Ver mi ubicación en Maps
                    </button>
                </div>
            `;

            container.innerHTML = locationInfo;
        }

        function showLocationErrorDetails(error, message, suggestion) {
            const container = document.getElementById('nearbyStores');
            if (!container) return;

            const errorInfo = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <h4 class="font-medium text-red-800 mb-2">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Error de Geolocalización
                    </h4>
                    <div class="text-sm text-red-700 space-y-2">
                        <p><strong>Error:</strong> ${message}</p>
                        <p><strong>Código:</strong> ${error.code}</p>
                        <p><strong>Sugerencia:</strong> ${suggestion}</p>
                    </div>
                    <div class="mt-4 space-y-2">
                        <button onclick="enableLocationTracking()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition mr-2">
                            <i class="fas fa-redo mr-2"></i>
                            Intentar nuevamente
                        </button>
                        <button onclick="showLocationFallbackOptions()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm transition">
                            <i class="fas fa-map mr-2"></i>
                            Usar modo demo
                        </button>
                    </div>
                </div>
            `;

            container.innerHTML = errorInfo;
        }

        function showLocationFallbackOptions() {
            const container = document.getElementById('nearbyStores');
            if (!container) return;

            const fallbackInfo = `
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                    <h4 class="font-medium text-yellow-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>
                        Modo Demo Disponible
                    </h4>
                    <p class="text-sm text-yellow-700 mb-4">
                        No se pudo obtener tu ubicación real. Puedes usar el modo demo para ver cómo funciona la búsqueda de tiendas cercanas.
                    </p>
                    <div class="space-y-2">
                        <button onclick="useDemoLocation('santiago')" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm transition mr-2">
                            <i class="fas fa-city mr-2"></i>
                            Demo: Santiago Centro
                        </button>
                        <button onclick="useDemoLocation('providencia')" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm transition mr-2">
                            <i class="fas fa-building mr-2"></i>
                            Demo: Providencia
                        </button>
                        <button onclick="useDemoLocation('lascondes')" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm transition">
                            <i class="fas fa-home mr-2"></i>
                            Demo: Las Condes
                        </button>
                    </div>
                </div>
            `;

            container.innerHTML = fallbackInfo;
        }

        function openCurrentLocationInMaps(lat, lng) {
            const mapsUrl = `https://www.google.com/maps?q=${lat},${lng}&zoom=16`;
            if (confirm('¿Deseas ver tu ubicación actual en Google Maps?')) {
                window.open(mapsUrl, '_blank', 'noopener,noreferrer');
                showNotification('Abriendo tu ubicación en Maps...', 'info');
                vibrate(50);
            }
        }

        function findRealNearbyStores(userLat, userLng) {
            console.log(`Searching for stores near: ${userLat}, ${userLng}`);

            // Validate coordinates
            if (!isValidCoordinate(userLat, userLng)) {
                console.error('Invalid coordinates provided');
                showNoStoresFound(userLat, userLng);
                return;
            }

            // Database of real stores in Chile with actual coordinates (expanded coverage)
            const allStores = [
                // Santiago Centro
                {
                    name: 'Jumbo Alameda',
                    type: 'Supermercado',
                    address: 'Av. Libertador Bernardo O\'Higgins 3820, Estación Central',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2422 8000',
                    lat: -33.4569,
                    lng: -70.6483,
                    rating: 4.1,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia', 'Panadería'],
                    website: 'https://www.jumbo.cl'
                },
                {
                    name: 'Lider Alameda',
                    type: 'Supermercado',
                    address: 'Av. Libertador Bernardo O\'Higgins 1302, Santiago',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2688 9000',
                    lat: -33.4378,
                    lng: -70.6504,
                    rating: 4.0,
                    services: ['Express', 'Delivery', 'Estacionamiento'],
                    website: 'https://www.lider.cl'
                },
                // Providencia
                {
                    name: 'Jumbo Bilbao',
                    type: 'Supermercado',
                    address: 'Av. Providencia 2653, Providencia',
                    hours: '8:00 - 23:00',
                    phone: '+56 2 2231 8000',
                    lat: -33.4242,
                    lng: -70.6147,
                    rating: 4.3,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia'],
                    website: 'https://www.jumbo.cl'
                },
                {
                    name: 'Santa Isabel Providencia',
                    type: 'Supermercado',
                    address: 'Av. Providencia 1550, Providencia',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2236 7000',
                    lat: -33.4186,
                    lng: -70.6089,
                    rating: 4.0,
                    services: ['Panadería', 'Carnicería', 'Delivery'],
                    website: 'https://www.santaisabel.cl'
                },
                // Las Condes
                {
                    name: 'Jumbo Kennedy',
                    type: 'Supermercado',
                    address: 'Av. Kennedy 9001, Las Condes',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2426 8000',
                    lat: -33.4053,
                    lng: -70.5698,
                    rating: 4.4,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia', 'Panadería'],
                    website: 'https://www.jumbo.cl'
                },
                {
                    name: 'Lider Las Condes',
                    type: 'Supermercado',
                    address: 'Av. Apoquindo 4400, Las Condes',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2630 4000',
                    lat: -33.4172,
                    lng: -70.5750,
                    rating: 4.2,
                    services: ['Express', 'Delivery', 'Estacionamiento'],
                    website: 'https://www.lider.cl'
                },
                // Farmacias (múltiples ubicaciones)
                {
                    name: 'Farmacia Ahumada - Providencia',
                    type: 'Farmacia',
                    address: 'Av. Providencia 1308, Providencia',
                    hours: '24 horas',
                    phone: '+56 2 2222 3333',
                    lat: -33.4156,
                    lng: -70.6067,
                    rating: 4.0,
                    services: ['24 horas', 'Delivery', 'Recetas'],
                    website: 'https://www.farmaciasahumada.cl'
                },
                {
                    name: 'Farmacia Cruz Verde - Las Condes',
                    type: 'Farmacia',
                    address: 'Av. Apoquindo 3846, Las Condes',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2444 5555',
                    lat: -33.4145,
                    lng: -70.5789,
                    rating: 4.1,
                    services: ['Delivery', 'Recetas', 'Cosméticos'],
                    website: 'https://www.cruzverde.cl'
                },
                // Bancos
                {
                    name: 'Banco de Chile - Providencia',
                    type: 'Banco',
                    address: 'Av. Providencia 1760, Providencia',
                    hours: '9:00 - 14:00',
                    phone: '+56 ************',
                    lat: -33.4203,
                    lng: -70.6108,
                    rating: 3.8,
                    services: ['Cajeros', 'Ejecutivos', 'Caja fuerte'],
                    website: 'https://www.bancochile.cl'
                },
                {
                    name: 'BancoEstado - Las Condes',
                    type: 'Banco',
                    address: 'Av. Apoquindo 4501, Las Condes',
                    hours: '9:00 - 14:00',
                    phone: '+56 ************',
                    lat: -33.4178,
                    lng: -70.5756,
                    rating: 3.7,
                    services: ['Cajeros', 'CuentaRUT', 'Créditos'],
                    website: 'https://www.bancoestado.cl'
                },
                // Zona Sur Santiago
                {
                    name: 'Lider San Miguel',
                    type: 'Supermercado',
                    address: 'Av. Gran Avenida 3177, San Miguel',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2555 8000',
                    lat: -33.4967,
                    lng: -70.6467,
                    rating: 4.0,
                    services: ['Express', 'Delivery', 'Estacionamiento'],
                    website: 'https://www.lider.cl'
                },
                {
                    name: 'Santa Isabel La Florida',
                    type: 'Supermercado',
                    address: 'Av. Vicuña Mackenna 7110, La Florida',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2777 9000',
                    lat: -33.5289,
                    lng: -70.5978,
                    rating: 3.9,
                    services: ['Panadería', 'Carnicería', 'Delivery'],
                    website: 'https://www.santaisabel.cl'
                },
                // Zona Norte Santiago
                {
                    name: 'Jumbo Independencia',
                    type: 'Supermercado',
                    address: 'Av. Independencia 1520, Independencia',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2888 7000',
                    lat: -33.3956,
                    lng: -70.6589,
                    rating: 4.1,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia'],
                    website: 'https://www.jumbo.cl'
                },
                // Zona Oeste Santiago
                {
                    name: 'Tottus Maipú',
                    type: 'Supermercado',
                    address: 'Av. Pajaritos 1744, Maipú',
                    hours: '8:30 - 22:00',
                    phone: '+56 2 2999 8000',
                    lat: -33.5067,
                    lng: -70.7589,
                    rating: 4.0,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia'],
                    website: 'https://www.tottus.cl'
                },
                // Regiones
                {
                    name: 'Jumbo Valparaíso',
                    type: 'Supermercado',
                    address: 'Av. Argentina 01, Valparaíso',
                    hours: '8:00 - 22:00',
                    phone: '+56 32 2555 7000',
                    lat: -33.0472,
                    lng: -71.6127,
                    rating: 4.2,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia'],
                    website: 'https://www.jumbo.cl'
                },
                {
                    name: 'Lider Concepción',
                    type: 'Supermercado',
                    address: 'Av. O\'Higgins 940, Concepción',
                    hours: '8:30 - 22:30',
                    phone: '+56 41 2777 8000',
                    lat: -36.8270,
                    lng: -73.0444,
                    rating: 4.1,
                    services: ['Express', 'Delivery', 'Estacionamiento'],
                    website: 'https://www.lider.cl'
                },
                {
                    name: 'Santa Isabel Temuco',
                    type: 'Supermercado',
                    address: 'Av. Alemania 0671, Temuco',
                    hours: '8:30 - 22:30',
                    phone: '+56 45 2888 9000',
                    lat: -38.7359,
                    lng: -72.5904,
                    rating: 3.8,
                    services: ['Panadería', 'Carnicería', 'Delivery'],
                    website: 'https://www.santaisabel.cl'
                },
                // Más ubicaciones para mejor cobertura
                {
                    name: 'Unimarc Ñuñoa',
                    type: 'Supermercado',
                    address: 'Av. Irarrázaval 2821, Ñuñoa',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2777 6000',
                    lat: -33.4567,
                    lng: -70.6089,
                    rating: 3.9,
                    services: ['Express', 'Delivery', 'Panadería'],
                    website: 'https://www.unimarc.cl'
                },
                {
                    name: 'Farmacia Cruz Verde - Centro',
                    type: 'Farmacia',
                    address: 'Av. Libertador Bernardo O\'Higgins 1302, Santiago',
                    hours: '8:00 - 21:00',
                    phone: '+56 2 2666 7000',
                    lat: -33.4378,
                    lng: -70.6504,
                    rating: 4.0,
                    services: ['Delivery', 'Recetas', 'Cosméticos'],
                    website: 'https://www.cruzverde.cl'
                },
                {
                    name: 'Lider Express Ñuñoa',
                    type: 'Minimarket',
                    address: 'Av. Grecia 1234, Ñuñoa',
                    hours: '7:00 - 23:00',
                    phone: '+56 2 2888 5000',
                    lat: -33.4689,
                    lng: -70.6234,
                    rating: 4.0,
                    services: ['Express', 'Delivery', '24 horas'],
                    website: 'https://www.lider.cl'
                }
            ];

            // Calculate distances and filter nearby stores (within 15km)
            const nearbyStores = allStores.map(store => {
                const distance = calculateDistance(userLat, userLng, store.lat, store.lng);
                return {
                    ...store,
                    distance: distance,
                    distanceText: formatDistance(distance)
                };
            })
            .filter(store => store.distance <= 15) // Only stores within 15km
            .sort((a, b) => a.distance - b.distance); // Sort by distance

            console.log(`Found ${nearbyStores.length} stores within 15km`);

            if (nearbyStores.length === 0) {
                console.log('No stores found within 15km');
                console.log('User coordinates:', userLat, userLng);
                console.log('Total stores in database:', allStores.length);

                // Show distances to closest stores for debugging
                const allDistances = allStores.map(store => ({
                    name: store.name,
                    distance: calculateDistance(userLat, userLng, store.lat, store.lng)
                })).sort((a, b) => a.distance - b.distance);

                console.log('Closest 5 stores:', allDistances.slice(0, 5));

                showNoStoresFound(userLat, userLng);
            } else {
                console.log(`Found ${nearbyStores.length} stores within 15km`);
                displayNearbyStores(nearbyStores, userLat, userLng);
            }
        }

        function calculateDistance(lat1, lng1, lat2, lng2) {
            // Haversine formula to calculate distance between two points
            const R = 6371; // Earth's radius in kilometers
            const dLat = toRadians(lat2 - lat1);
            const dLng = toRadians(lng2 - lng1);

            const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                     Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
                     Math.sin(dLng / 2) * Math.sin(dLng / 2);

            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            const distance = R * c;

            return distance;
        }

        function toRadians(degrees) {
            return degrees * (Math.PI / 180);
        }

        function formatDistance(distance) {
            if (distance < 1) {
                return `${Math.round(distance * 1000)}m`;
            } else {
                return `${distance.toFixed(1)}km`;
            }
        }

        function showNoStoresFound(userLat, userLng) {
            const container = document.getElementById('nearbyStores');
            if (!container) return;

            const noStoresHtml = `
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                    <i class="fas fa-search text-yellow-600 text-4xl mb-4"></i>
                    <h4 class="font-medium text-yellow-800 mb-2">No se encontraron tiendas cercanas</h4>
                    <p class="text-sm text-yellow-700 mb-4">
                        No hay tiendas registradas en un radio de 15km de tu ubicación actual.<br>
                        <strong>Ubicación detectada:</strong> ${userLat.toFixed(4)}, ${userLng.toFixed(4)}<br>
                        <em>Estamos expandiendo nuestra base de datos de tiendas constantemente.</em>
                    </p>
                    <div class="space-y-2">
                        <button onclick="expandSearchRadius(${userLat}, ${userLng})" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm transition">
                            <i class="fas fa-expand-arrows-alt mr-2"></i>
                            Ampliar búsqueda a 25km
                        </button>
                        <button onclick="openCurrentLocationInMaps(${userLat}, ${userLng})" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition ml-2">
                            <i class="fas fa-map mr-2"></i>
                            Ver área en Maps
                        </button>
                    </div>
                </div>
            `;

            container.innerHTML += noStoresHtml;
        }

        function expandSearchRadius(userLat, userLng) {
            showNotification('Ampliando búsqueda a 25km...', 'info');

            // Re-run search with expanded radius
            const allStores = getAllStoresDatabase();
            const nearbyStores = allStores.map(store => {
                const distance = calculateDistance(userLat, userLng, store.lat, store.lng);
                return {
                    ...store,
                    distance: distance,
                    distanceText: formatDistance(distance)
                };
            })
            .filter(store => store.distance <= 25) // Expanded to 25km
            .sort((a, b) => a.distance - b.distance);

            if (nearbyStores.length === 0) {
                showNotification('No se encontraron tiendas en un radio de 25km', 'warning');
            } else {
                displayNearbyStores(nearbyStores, userLat, userLng);
                showNotification(`Encontradas ${nearbyStores.length} tiendas en un radio ampliado`, 'success');
            }
        }

        function useDemoLocation(area) {
            const demoLocations = {
                'santiago': { lat: -33.4489, lng: -70.6693, name: 'Santiago Centro' },
                'providencia': { lat: -33.4242, lng: -70.6147, name: 'Providencia' },
                'lascondes': { lat: -33.4053, lng: -70.5698, name: 'Las Condes' }
            };

            const location = demoLocations[area];
            if (!location) return;

            showNotification(`Usando ubicación demo: ${location.name}`, 'info');

            // Store demo location
            app.data.userLocation = {
                latitude: location.lat,
                longitude: location.lng,
                accuracy: 100,
                timestamp: new Date().toISOString(),
                isDemo: true,
                demoArea: location.name
            };

            saveData();
            showLocationInfo(location.lat, location.lng, 100);
            findRealNearbyStores(location.lat, location.lng);
        }

        function displayNearbyStores(stores, userLat, userLng) {
            const container = document.getElementById('nearbyStores');
            if (!container) return;

            // Store the results for later use
            app.data.lastNearbyStores = stores;

            const html = `
                <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>
                            Tiendas Cercanas
                        </h3>
                        <button onclick="showAllStoresOnMap()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition">
                            <i class="fas fa-map mr-2"></i>Ver Mapa
                        </button>
                    </div>

                    <div class="bg-blue-50 p-3 rounded-lg mb-4">
                        <p class="text-blue-800 text-sm">
                            <i class="fas fa-location-arrow mr-2"></i>
                            Encontradas ${stores.length} tiendas en un radio de 15km
                        </p>
                    </div>

                    <div class="space-y-4">
                        ${stores.map((store, index) => `
                            <div class="bg-gray-50 rounded-lg p-4 border-l-4 ${getStoreColorBorder(store.type)}">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <h4 class="font-medium text-gray-800 text-lg">${store.name}</h4>
                                            <span class="text-xs ${getStoreTypeBadge(store.type)} px-2 py-1 rounded-full">${store.type}</span>
                                            <div class="flex items-center space-x-1">
                                                ${generateStarRating(store.rating)}
                                                <span class="text-xs text-gray-500">(${store.rating})</span>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-600 mb-1">
                                            <i class="fas fa-map-marker-alt mr-1"></i>
                                            ${store.address}
                                        </p>
                                        <p class="text-sm text-gray-600 mb-2">
                                            <i class="fas fa-clock mr-1"></i>
                                            ${store.hours}
                                        </p>
                                        <div class="flex flex-wrap gap-1 mb-3">
                                            ${store.services.map(service => `
                                                <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">${service}</span>
                                            `).join('')}
                                        </div>
                                    </div>
                                    <div class="text-right ml-4">
                                        <p class="text-lg font-bold text-blue-600 mb-1">${store.distance}</p>
                                        <p class="text-xs text-gray-500">distancia</p>
                                    </div>
                                </div>

                                <div class="flex flex-wrap gap-2">
                                    <button onclick="openStoreInMaps(${store.lat}, ${store.lng}, '${store.name}')" class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm transition flex items-center space-x-1">
                                        <i class="fas fa-directions"></i>
                                        <span>Cómo llegar</span>
                                    </button>
                                    <button onclick="callStore('${store.phone}')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition flex items-center space-x-1">
                                        <i class="fas fa-phone"></i>
                                        <span>Llamar</span>
                                    </button>
                                    <button onclick="openStoreWebsite('${store.website}')" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm transition flex items-center space-x-1">
                                        <i class="fas fa-globe"></i>
                                        <span>Web</span>
                                    </button>
                                    <button onclick="shareStore('${store.name}', '${store.address}', ${store.lat}, ${store.lng})" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-lg text-sm transition flex items-center space-x-1">
                                        <i class="fas fa-share"></i>
                                        <span>Compartir</span>
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="mt-4 bg-gray-50 p-4 rounded-lg">
                        <p class="text-xs text-gray-600 mb-2">
                            <i class="fas fa-info-circle mr-1"></i>
                            Información actualizada: ${new Date().toLocaleString('es-CL')}
                        </p>
                        <p class="text-xs text-gray-500">
                            Los horarios pueden variar. Se recomienda confirmar antes de visitar.
                        </p>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        function callStore(phone) {
            if (confirm(`¿Deseas llamar a ${phone}?`)) {
                window.location.href = `tel:${phone}`;
                showNotification('Iniciando llamada...', 'info');
                vibrate(100);
            }
        }

        function openStoreInMaps(lat, lng, storeName) {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            let mapsUrl;

            // Detect device type and open appropriate maps app
            if (/android/i.test(userAgent)) {
                // Android - Google Maps
                mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=${encodeURIComponent(storeName)}`;
            } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                // iOS - Apple Maps first, fallback to Google Maps
                mapsUrl = `maps://maps.google.com/maps?daddr=${lat},${lng}&amp;ll=`;
                // Fallback to Google Maps if Apple Maps not available
                setTimeout(() => {
                    window.location.href = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
                }, 2000);
            } else {
                // Desktop/Web - Google Maps
                mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
            }

            if (confirm(`¿Deseas abrir ${storeName} en Maps para obtener direcciones?`)) {
                window.open(mapsUrl, '_blank', 'noopener,noreferrer');
                showNotification('Abriendo Maps...', 'success');
                vibrate(100);
            }
        }

        function showAllStoresOnMap() {
            const stores = getCurrentStores();
            if (!stores || stores.length === 0) {
                showNotification('No hay tiendas para mostrar en el mapa', 'error');
                return;
            }

            // Create a Google Maps URL with multiple markers
            const markers = stores.map(store =>
                `markers=color:red%7Clabel:${store.name.charAt(0)}%7C${store.lat},${store.lng}`
            ).join('&');

            const mapsUrl = `https://www.google.com/maps?${markers}&zoom=14`;

            if (confirm('¿Deseas ver todas las tiendas en Google Maps?')) {
                window.open(mapsUrl, '_blank', 'noopener,noreferrer');
                showNotification('Abriendo mapa con todas las tiendas...', 'success');
                vibrate(100);
            }
        }

        function shareStore(name, address, lat, lng) {
            const shareData = {
                title: `${name} - Tienda encontrada`,
                text: `Te comparto esta tienda: ${name}\nDirección: ${address}`,
                url: `https://www.google.com/maps?q=${lat},${lng}`
            };

            if (navigator.share) {
                // Use native sharing if available
                navigator.share(shareData)
                    .then(() => {
                        showNotification('Tienda compartida exitosamente', 'success');
                        vibrate(50);
                    })
                    .catch((error) => {
                        console.log('Error sharing:', error);
                        fallbackShare(shareData);
                    });
            } else {
                fallbackShare(shareData);
            }
        }

        function fallbackShare(shareData) {
            // Fallback sharing method
            const shareText = `${shareData.text}\n${shareData.url}`;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(shareText)
                    .then(() => {
                        showNotification('Información copiada al portapapeles', 'success');
                        vibrate(100);
                    })
                    .catch(() => {
                        showShareModal(shareData);
                    });
            } else {
                showShareModal(shareData);
            }
        }

        function showShareModal(shareData) {
            const modalHtml = `
                <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div class="bg-white rounded-xl p-6 w-full max-w-md mx-auto">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Compartir Tienda</h3>
                        <textarea readonly class="w-full p-3 border border-gray-200 rounded-lg text-sm" rows="4">${shareData.text}\n${shareData.url}</textarea>
                        <div class="flex space-x-3 mt-4">
                            <button onclick="closeShareModal()" class="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg">Cerrar</button>
                            <button onclick="copyShareText()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg">Copiar</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        function closeShareModal() {
            const modal = document.getElementById('shareModal');
            if (modal) modal.remove();
        }

        function copyShareText() {
            const textarea = document.querySelector('#shareModal textarea');
            textarea.select();
            document.execCommand('copy');
            showNotification('Texto copiado al portapapeles', 'success');
            closeShareModal();
        }

        function getCurrentStores() {
            // Return the currently displayed stores from the last search
            if (app.data.lastNearbyStores) {
                return app.data.lastNearbyStores.map(store => ({
                    name: store.name,
                    lat: store.lat,
                    lng: store.lng
                }));
            }

            // Fallback to empty array if no stores found
            return [];
        }

        function getAllStoresDatabase() {
            // Complete database of stores in Chile
            return [
                // Santiago Centro
                {
                    name: 'Jumbo Alameda',
                    type: 'Supermercado',
                    address: 'Av. Libertador Bernardo O\'Higgins 3820, Estación Central',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2422 8000',
                    lat: -33.4569,
                    lng: -70.6483,
                    rating: 4.1,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia', 'Panadería'],
                    website: 'https://www.jumbo.cl'
                },
                {
                    name: 'Lider Alameda',
                    type: 'Supermercado',
                    address: 'Av. Libertador Bernardo O\'Higgins 1302, Santiago',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2688 9000',
                    lat: -33.4378,
                    lng: -70.6504,
                    rating: 4.0,
                    services: ['Express', 'Delivery', 'Estacionamiento'],
                    website: 'https://www.lider.cl'
                },
                {
                    name: 'Santa Isabel Centro',
                    type: 'Supermercado',
                    address: 'Av. Libertador Bernardo O\'Higgins 1410, Santiago',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2555 6000',
                    lat: -33.4389,
                    lng: -70.6489,
                    rating: 3.9,
                    services: ['Panadería', 'Carnicería', 'Delivery'],
                    website: 'https://www.santaisabel.cl'
                },
                // Providencia
                {
                    name: 'Jumbo Bilbao',
                    type: 'Supermercado',
                    address: 'Av. Providencia 2653, Providencia',
                    hours: '8:00 - 23:00',
                    phone: '+56 2 2231 8000',
                    lat: -33.4242,
                    lng: -70.6147,
                    rating: 4.3,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia'],
                    website: 'https://www.jumbo.cl'
                },
                {
                    name: 'Santa Isabel Providencia',
                    type: 'Supermercado',
                    address: 'Av. Providencia 1550, Providencia',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2236 7000',
                    lat: -33.4186,
                    lng: -70.6089,
                    rating: 4.0,
                    services: ['Panadería', 'Carnicería', 'Delivery'],
                    website: 'https://www.santaisabel.cl'
                },
                {
                    name: 'Unimarc Providencia',
                    type: 'Supermercado',
                    address: 'Av. Providencia 2124, Providencia',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2333 4000',
                    lat: -33.4225,
                    lng: -70.6125,
                    rating: 4.0,
                    services: ['Express', 'Delivery', 'Panadería'],
                    website: 'https://www.unimarc.cl'
                },
                // Las Condes
                {
                    name: 'Jumbo Kennedy',
                    type: 'Supermercado',
                    address: 'Av. Kennedy 9001, Las Condes',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2426 8000',
                    lat: -33.4053,
                    lng: -70.5698,
                    rating: 4.4,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia', 'Panadería'],
                    website: 'https://www.jumbo.cl'
                },
                {
                    name: 'Lider Las Condes',
                    type: 'Supermercado',
                    address: 'Av. Apoquindo 4400, Las Condes',
                    hours: '8:30 - 22:30',
                    phone: '+56 2 2630 4000',
                    lat: -33.4172,
                    lng: -70.5750,
                    rating: 4.2,
                    services: ['Express', 'Delivery', 'Estacionamiento'],
                    website: 'https://www.lider.cl'
                },
                {
                    name: 'Tottus Las Condes',
                    type: 'Supermercado',
                    address: 'Av. Apoquindo 3990, Las Condes',
                    hours: '8:30 - 22:00',
                    phone: '+56 2 2444 7000',
                    lat: -33.4156,
                    lng: -70.5778,
                    rating: 4.1,
                    services: ['Estacionamiento', 'Delivery', 'Farmacia'],
                    website: 'https://www.tottus.cl'
                },
                // Farmacias
                {
                    name: 'Farmacia Ahumada - Providencia',
                    type: 'Farmacia',
                    address: 'Av. Providencia 1308, Providencia',
                    hours: '24 horas',
                    phone: '+56 2 2222 3333',
                    lat: -33.4156,
                    lng: -70.6067,
                    rating: 4.0,
                    services: ['24 horas', 'Delivery', 'Recetas'],
                    website: 'https://www.farmaciasahumada.cl'
                },
                {
                    name: 'Farmacia Cruz Verde - Las Condes',
                    type: 'Farmacia',
                    address: 'Av. Apoquindo 3846, Las Condes',
                    hours: '8:00 - 22:00',
                    phone: '+56 2 2444 5555',
                    lat: -33.4145,
                    lng: -70.5789,
                    rating: 4.1,
                    services: ['Delivery', 'Recetas', 'Cosméticos'],
                    website: 'https://www.cruzverde.cl'
                },
                {
                    name: 'Farmacia Salcobrand - Centro',
                    type: 'Farmacia',
                    address: 'Av. Libertador Bernardo O\'Higgins 1302, Santiago',
                    hours: '8:00 - 21:00',
                    phone: '+56 2 2777 8000',
                    lat: -33.4378,
                    lng: -70.6504,
                    rating: 3.9,
                    services: ['Delivery', 'Recetas', 'Perfumería'],
                    website: 'https://www.salcobrand.cl'
                },
                // Bancos
                {
                    name: 'Banco de Chile - Providencia',
                    type: 'Banco',
                    address: 'Av. Providencia 1760, Providencia',
                    hours: '9:00 - 14:00',
                    phone: '+56 ************',
                    lat: -33.4203,
                    lng: -70.6108,
                    rating: 3.8,
                    services: ['Cajeros', 'Ejecutivos', 'Caja fuerte'],
                    website: 'https://www.bancochile.cl'
                },
                {
                    name: 'BancoEstado - Las Condes',
                    type: 'Banco',
                    address: 'Av. Apoquindo 4501, Las Condes',
                    hours: '9:00 - 14:00',
                    phone: '+56 ************',
                    lat: -33.4178,
                    lng: -70.5756,
                    rating: 3.7,
                    services: ['Cajeros', 'CuentaRUT', 'Créditos'],
                    website: 'https://www.bancoestado.cl'
                },
                {
                    name: 'Banco Santander - Centro',
                    type: 'Banco',
                    address: 'Av. Libertador Bernardo O\'Higgins 949, Santiago',
                    hours: '9:00 - 14:00',
                    phone: '+56 ************',
                    lat: -33.4356,
                    lng: -70.6467,
                    rating: 3.6,
                    services: ['Cajeros', 'Ejecutivos', 'Inversiones'],
                    website: 'https://www.santander.cl'
                }
            ];
        }

        function getStoreColorBorder(type) {
            const colors = {
                'Supermercado': 'border-green-500',
                'Farmacia': 'border-red-500',
                'Banco': 'border-blue-500',
                'Minimarket': 'border-yellow-500',
                'default': 'border-gray-500'
            };
            return colors[type] || colors.default;
        }

        function getStoreTypeBadge(type) {
            const badges = {
                'Supermercado': 'bg-green-100 text-green-600',
                'Farmacia': 'bg-red-100 text-red-600',
                'Banco': 'bg-blue-100 text-blue-600',
                'Minimarket': 'bg-yellow-100 text-yellow-600',
                'default': 'bg-gray-100 text-gray-600'
            };
            return badges[type] || badges.default;
        }

        function generateStarRating(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 !== 0;
            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

            let stars = '';

            // Full stars
            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="fas fa-star text-yellow-400 text-xs"></i>';
            }

            // Half star
            if (hasHalfStar) {
                stars += '<i class="fas fa-star-half-alt text-yellow-400 text-xs"></i>';
            }

            // Empty stars
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="far fa-star text-gray-300 text-xs"></i>';
            }

            return stars;
        }

        function refreshInsights() {
            // Show loading state
            const insightsContainer = document.querySelector('.bg-gradient-to-r.from-blue-500.to-purple-600');
            if (insightsContainer) {
                const originalContent = insightsContainer.innerHTML;
                insightsContainer.innerHTML = `
                    <div class="flex items-center justify-center py-8">
                        <i class="fas fa-spinner fa-spin text-white text-2xl mr-3"></i>
                        <span class="text-white">Actualizando análisis...</span>
                    </div>
                `;

                // Simulate analysis refresh
                setTimeout(() => {
                    // Reload the resumen section to refresh insights
                    loadSection('resumen');
                    showNotification('Análisis inteligente actualizado', 'success');
                    vibrate(50);
                }, 1500);
            } else {
                showNotification('Análisis actualizado', 'success');
            }
        }

        // Initialize section features
        function initializeSectionFeatures(sectionName) {
            try {
                switch(sectionName) {
                    case 'resumen':
                        // Initialize currency rates and internet features
                        setTimeout(() => {
                            initializeInternetFeatures();
                        }, 1000);
                        break;

                    case 'estadisticas':
                        // Initialize charts only if Chart.js is available
                        setTimeout(() => {
                            if (typeof Chart !== 'undefined') {
                                initializeCharts();
                            } else {
                                console.warn('Chart.js not loaded yet, retrying...');
                                setTimeout(() => {
                                    if (typeof Chart !== 'undefined') {
                                        initializeCharts();
                                    }
                                }, 2000);
                            }
                        }, 500);
                        break;

                    case 'metas':
                        // Set up goals form listeners
                        setTimeout(() => {
                            setupGoalsFormListeners();
                        }, 200);
                        break;

                    case 'ajustes':
                        // Set up settings form listeners
                        setTimeout(() => {
                            setupSettingsListeners();
                        }, 200);
                        break;

                    default:
                        // Default initialization for other sections
                        break;
                }
            } catch (error) {
                console.error('Error initializing section features:', error);
            }
        }

        function setupGoalsFormListeners() {
            // Set up event listeners for goals section
            const goalForm = document.querySelector('#goalName');
            if (goalForm) {
                // Goals form is ready
                console.log('Goals section initialized');
            }
        }

        function setupSettingsListeners() {
            // Set up event listeners for settings section
            const familyInput = document.getElementById('familyNameInput');
            if (familyInput) {
                // Settings form is ready
                console.log('Settings section initialized');
            }
        }

        function initializeCharts() {
            try {
                // Destroy existing charts if they exist
                if (app.charts.category) {
                    app.charts.category.destroy();
                    app.charts.category = null;
                }
                if (app.charts.trend) {
                    app.charts.trend.destroy();
                    app.charts.trend = null;
                }

                // Initialize category chart
                const categoryCtx = document.getElementById('categoryChart');
                if (categoryCtx) {
                    const categoryData = getCategoryChartData();
                    if (categoryData.labels.length > 0) {
                        app.charts.category = new Chart(categoryCtx, {
                            type: 'doughnut',
                            data: categoryData,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            padding: 20,
                                            usePointStyle: true,
                                            font: {
                                                size: 12
                                            }
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                                return `${context.label}: ${formatCLP(context.parsed)} (${percentage}%)`;
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    } else {
                        // Show no data message
                        categoryCtx.parentElement.innerHTML = '<div class="flex items-center justify-center h-64 text-gray-500"><i class="fas fa-chart-pie text-4xl mb-2"></i><p>No hay datos para mostrar</p></div>';
                    }
                }

                // Initialize trend chart
                const trendCtx = document.getElementById('trendChart');
                if (trendCtx) {
                    const trendData = getTrendChartData();
                    app.charts.trend = new Chart(trendCtx, {
                        type: 'line',
                        data: trendData,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                intersect: false,
                                mode: 'index'
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        callback: function(value) {
                                            return formatCLP(value);
                                        }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        padding: 20,
                                        usePointStyle: true,
                                        font: {
                                            size: 12
                                        }
                                    }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            return context.dataset.label + ': ' + formatCLP(context.parsed.y);
                                        }
                                    }
                                }
                            }
                        }
                    });
                }

                console.log('Charts initialized successfully');
            } catch (error) {
                console.error('Error initializing charts:', error);
                showNotification('Error al cargar gráficos', 'error');
            }
        }

        function getCategoryChartData() {
            try {
                const currentMonth = new Date().toISOString().slice(0, 7);
                const expenses = app.data.transactions.filter(t => t.type === 'expense' && t.date.startsWith(currentMonth));

                const categoryTotals = {};
                expenses.forEach(expense => {
                    const category = getCategoryLabel(expense.category, 'expense');
                    categoryTotals[category] = (categoryTotals[category] || 0) + expense.amount;
                });

                const labels = Object.keys(categoryTotals);
                const data = Object.values(categoryTotals);

                // Return empty data if no expenses
                if (labels.length === 0) {
                    return { labels: [], datasets: [] };
                }

                const colors = [
                    '#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6',
                    '#ec4899', '#14b8a6', '#f97316', '#84cc16', '#6366f1'
                ];

                return {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors.slice(0, labels.length),
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 3,
                        hoverBorderColor: '#333333'
                    }]
                };
            } catch (error) {
                console.error('Error getting category chart data:', error);
                return { labels: [], datasets: [] };
            }
        }

        function getTrendChartData() {
            // Get last 6 months of data
            const months = [];
            const incomeData = [];
            const expenseData = [];

            for (let i = 5; i >= 0; i--) {
                const date = new Date();
                date.setMonth(date.getMonth() - i);
                const monthStr = date.toISOString().slice(0, 7);

                const monthTransactions = app.data.transactions.filter(t => t.date.startsWith(monthStr));
                const monthIncome = monthTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0);
                const monthExpenses = monthTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0);

                months.push(date.toLocaleDateString('es-CL', { month: 'short', year: '2-digit' }));
                incomeData.push(monthIncome);
                expenseData.push(monthExpenses);
            }

            return {
                labels: months,
                datasets: [
                    {
                        label: 'Ingresos',
                        data: incomeData,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Gastos',
                        data: expenseData,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }
                ]
            };
        }

        // Performance and app detection functions
        function optimizePerformance() {
            // Performance optimizations
        }

        // Memory management functions
        function addTimer(type, id) {
            if (type === 'timeout') {
                app.timers.timeouts.push(id);
            } else if (type === 'interval') {
                app.timers.intervals.push(id);
            }
        }

        function clearAllTimers() {
            // Clear all timeouts
            app.timers.timeouts.forEach(id => clearTimeout(id));
            app.timers.timeouts = [];

            // Clear all intervals
            app.timers.intervals.forEach(id => clearInterval(id));
            app.timers.intervals = [];
        }

        function addEventListenerTracked(element, event, handler, options) {
            element.addEventListener(event, handler, options);
            app.eventListeners.push({ element, event, handler, options });
        }

        function removeAllEventListeners() {
            app.eventListeners.forEach(({ element, event, handler }) => {
                try {
                    element.removeEventListener(event, handler);
                } catch (e) {
                    console.warn('Failed to remove event listener:', e);
                }
            });
            app.eventListeners = [];
        }

        function cleanupResources() {
            clearAllTimers();
            removeAllEventListeners();

            // Destroy charts
            if (app.charts.category) {
                app.charts.category.destroy();
                app.charts.category = null;
            }
            if (app.charts.trend) {
                app.charts.trend.destroy();
                app.charts.trend = null;
            }

            console.log('Resources cleaned up');
        }

        // Add cleanup on page unload
        window.addEventListener('beforeunload', cleanupResources);

        // Security functions
        function sanitizeHTML(str) {
            const temp = document.createElement('div');
            temp.textContent = str;
            return temp.innerHTML;
        }

        function escapeHTML(str) {
            const div = document.createElement('div');
            div.appendChild(document.createTextNode(str));
            return div.innerHTML;
        }

        function safeSetInnerHTML(element, html) {
            // Basic sanitization - remove script tags and event handlers
            const sanitized = html
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/on\w+\s*=\s*"[^"]*"/gi, '')
                .replace(/on\w+\s*=\s*'[^']*'/gi, '')
                .replace(/javascript:/gi, '');

            element.innerHTML = sanitized;
        }

        // Replace dangerous innerHTML usage
        function updateElementContent(elementId, content, isHTML = false) {
            const element = document.getElementById(elementId);
            if (!element) return;

            if (isHTML) {
                safeSetInnerHTML(element, content);
            } else {
                element.textContent = content;
            }
        }

        function detectAppEnvironment() {
            if (isRunningInApp()) {
                document.body.classList.add('app-mode');
                console.log('Running in app mode');
            }
        }

        function initializeNotifications() {
            // Initialize notification system
        }

        function initializeInternetFeatures() {
            try {
                if (navigator.onLine) {
                    // Load currency rates
                    const timeoutId = setTimeout(() => {
                        try {
                            const rates = { uf: 37000, utm: 65000, usd: 900, eur: 1000 };
                            const ufEl = document.getElementById('ufRate');
                            const utmEl = document.getElementById('utmRate');
                            const usdEl = document.getElementById('usdRate');
                            const eurEl = document.getElementById('eurRate');

                            if (ufEl) ufEl.textContent = formatCLP(rates.uf);
                            if (utmEl) utmEl.textContent = formatCLP(rates.utm);
                            if (usdEl) usdEl.textContent = formatCLP(rates.usd);
                            if (eurEl) eurEl.textContent = formatCLP(rates.eur);

                            app.currencyRates = rates;
                            console.log('Currency rates loaded successfully');
                        } catch (error) {
                            console.error('Error loading currency rates:', error);
                        }
                    }, 2000);

                    addTimer('timeout', timeoutId);
                    console.log('Funcionalidades de internet inicializadas');
                } else {
                    console.log('Offline mode - internet features disabled');
                }
            } catch (error) {
                console.error('Error initializing internet features:', error);
            }
        }

        function setupTouchGestures() {
            // Completely disable touch gestures to ensure proper mobile scrolling
            console.log('Touch gestures disabled for optimal mobile scrolling experience');
            return;
        }

        function handleSwipeGesture(diffX) {
            try {
                const sections = ['resumen', 'ingresos', 'gastos', 'estadisticas', 'categorias', 'presupuestos', 'metas', 'recurrentes', 'ajustes'];
                const currentIndex = sections.indexOf(app.state.currentSection);

                if (currentIndex === -1) {
                    console.warn('Current section not found in sections array');
                    return;
                }

                let newIndex;
                if (diffX > 0) {
                    // Swipe left - next section
                    newIndex = currentIndex < sections.length - 1 ? currentIndex + 1 : 0;
                    showSwipeIndicator('right');
                } else {
                    // Swipe right - previous section
                    newIndex = currentIndex > 0 ? currentIndex - 1 : sections.length - 1;
                    showSwipeIndicator('left');
                }

                loadSection(sections[newIndex]);
                vibrate(50);

                console.log(`Swiped from ${app.state.currentSection} to ${sections[newIndex]}`);
            } catch (error) {
                console.error('Error handling swipe gesture:', error);
            }
        }

        function showSwipeIndicator(direction) {
            const indicator = document.getElementById(`swipe${direction === 'left' ? 'Left' : 'Right'}`);
            if (indicator) {
                indicator.classList.add('visible');
                setTimeout(() => {
                    indicator.classList.remove('visible');
                }, 300);
            }
        }

        function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                // Create service worker inline
                const swCode = `
                    const CACHE_NAME = 'famifinanzas-v1';
                    const urlsToCache = [
                        '/',
                        'https://cdn.tailwindcss.com',
                        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
                        'https://cdn.jsdelivr.net/npm/chart.js'
                    ];

                    self.addEventListener('install', event => {
                        event.waitUntil(
                            caches.open(CACHE_NAME)
                                .then(cache => cache.addAll(urlsToCache))
                        );
                    });

                    self.addEventListener('fetch', event => {
                        event.respondWith(
                            caches.match(event.request)
                                .then(response => {
                                    if (response) {
                                        return response;
                                    }
                                    return fetch(event.request);
                                })
                        );
                    });
                `;

                const blob = new Blob([swCode], { type: 'application/javascript' });
                const swUrl = URL.createObjectURL(blob);

                navigator.serviceWorker.register(swUrl)
                    .then(registration => {
                        console.log('Service Worker registrado:', registration);
                    })
                    .catch(error => {
                        console.log('Error al registrar Service Worker:', error);
                    });
            }
        }
    </script>
</body>
</html>
    </script>
</body>
</html>
